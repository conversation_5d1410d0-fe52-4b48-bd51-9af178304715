#!/usr/bin/env python3
"""
測試語音模組導入和功能
"""

import os
import sys
from dotenv import load_dotenv

def test_imports():
    """測試模組導入"""
    print("🔧 測試語音模組導入...")
    
    try:
        # 添加專案根目錄到路徑
        project_root = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, project_root)
        
        print("1. 測試語音對話模組...")
        from modules.voice_conversation.voice_conversation_module import VoiceConversationModule
        print("✅ VoiceConversationModule 導入成功")
        
        print("2. 測試主題管理模組...")
        from modules.topic_management.topic_management_module import TopicManagementModule
        print("✅ TopicManagementModule 導入成功")
        
        print("3. 測試音檔處理模組...")
        from modules.audio_processing.static_audio_server import StaticAudioServer, init_audio_server
        print("✅ StaticAudioServer 導入成功")
        
        print("4. 測試 OpenAI 客戶端...")
        from openai import OpenAI
        openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        print("✅ OpenAI 客戶端初始化成功")
        
        return True, {
            'VoiceConversationModule': VoiceConversationModule,
            'TopicManagementModule': TopicManagementModule,
            'StaticAudioServer': StaticAudioServer,
            'init_audio_server': init_audio_server,
            'openai_client': openai_client
        }
        
    except Exception as e:
        print(f"❌ 模組導入失敗: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_basic_functionality(modules):
    """測試基本功能"""
    print("\n🧪 測試基本功能...")
    
    try:
        # 測試主題管理
        print("1. 測試主題管理...")
        topic_manager = modules['TopicManagementModule']()
        daily_topic = topic_manager.get_daily_topic("test_user", "intermediate")
        print(f"✅ 獲取每日主題成功: {daily_topic.name}")
        
        # 測試語音對話模組初始化
        print("2. 測試語音對話模組...")
        voice_module = modules['VoiceConversationModule'](modules['openai_client'], None)
        print("✅ 語音對話模組初始化成功")
        
        # 測試音檔服務器
        print("3. 測試音檔服務器...")
        audio_server = modules['StaticAudioServer']("http://localhost:5004")
        print("✅ 音檔服務器初始化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("🎤 Emma 語音功能模組測試")
    print("=" * 40)
    
    # 載入環境變數
    load_dotenv()
    
    # 檢查環境變數
    required_vars = ["LINE_CHANNEL_ACCESS_TOKEN", "LINE_CHANNEL_SECRET", "OPENAI_API_KEY"]
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少環境變數: {', '.join(missing_vars)}")
        return
    
    print("✅ 環境變數檢查通過")
    
    # 測試模組導入
    success, modules = test_imports()
    if not success:
        print("\n❌ 模組導入測試失敗")
        return
    
    print("\n✅ 所有模組導入成功！")
    
    # 測試基本功能
    if test_basic_functionality(modules):
        print("\n🎉 所有功能測試通過！")
        print("\n📋 測試結果：")
        print("✅ 語音對話模組 - 正常")
        print("✅ 主題管理模組 - 正常") 
        print("✅ 音檔處理模組 - 正常")
        print("✅ OpenAI 整合 - 正常")
        
        print("\n🚀 語音功能已準備就緒！")
        print("💡 可以使用 restore_voice_features.py 啟動完整語音功能")
    else:
        print("\n❌ 功能測試失敗")

if __name__ == "__main__":
    main()
