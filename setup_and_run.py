#!/usr/bin/env python3
"""
AutoGen Line Bot 一鍵安裝和啟動腳本
解決批次文件編碼問題
"""

import subprocess
import sys
import os
import shutil
from pathlib import Path

def print_header():
    """顯示標題"""
    print("=" * 50)
    print("🤖 AutoGen Line Bot 一鍵安裝和啟動")
    print("=" * 50)

def check_python():
    """檢查 Python 版本"""
    print("\n📍 步驟 1: 檢查 Python 版本")
    version = sys.version_info
    print(f"Python 版本: {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 8):
        print("❌ 錯誤: 需要 Python 3.8 或更高版本")
        return False
    else:
        print("✅ Python 版本符合要求")
        return True

def run_command(command, description=""):
    """執行命令"""
    if description:
        print(f"🔄 {description}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True,
            encoding='utf-8'
        )
        print(f"✅ 成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 失敗: {e}")
        if e.stderr:
            print(f"錯誤詳情: {e.stderr}")
        return False

def install_packages():
    """安裝套件"""
    print("\n📍 步驟 2: 安裝依賴套件")
    
    # 升級 pip
    run_command("python -m pip install --upgrade pip", "升級 pip")
    
    # 基本套件
    packages = [
        "fastapi==0.104.1",
        "uvicorn[standard]==0.24.0",
        "line-bot-sdk==3.7.0", 
        "openai",
        "requests",
        "pydantic==2.5.0"
    ]
    
    print("安裝基本套件...")
    for package in packages:
        print(f"  安裝 {package}...")
        if not run_command(f"pip install {package}"):
            print(f"⚠️ 警告: {package} 安裝失敗")

def check_environment():
    """檢查環境變數"""
    print("\n📍 步驟 3: 檢查環境變數")
    
    required_vars = [
        "LINE_CHANNEL_ACCESS_TOKEN",
        "LINE_CHANNEL_SECRET",
        "OPENAI_API_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var} - 已設定")
        else:
            print(f"❌ {var} - 未設定")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n⚠️ 缺少環境變數: {', '.join(missing_vars)}")
        
        # 檢查並複製 .env 文件
        if not os.path.exists('.env') and os.path.exists('.env.example'):
            shutil.copy('.env.example', '.env')
            print("✅ 已複製 .env.example 為 .env")
            print("📝 請編輯 .env 文件設定您的 API 金鑰")
            
            # 嘗試打開 .env 文件
            try:
                if os.name == 'nt':  # Windows
                    os.system('notepad .env')
                else:  # Linux/Mac
                    os.system('nano .env || vi .env')
            except:
                print("請手動編輯 .env 文件")
        
        return False
    
    return True

def test_installation():
    """測試安裝"""
    print("\n📍 步驟 4: 測試安裝")
    
    if os.path.exists('test_installation.py'):
        return run_command("python test_installation.py", "執行安裝測試")
    else:
        print("⚠️ 測試腳本不存在，跳過測試")
        return True

def start_bot():
    """啟動 Bot"""
    print("\n📍 步驟 5: 啟動 Line Bot")
    
    if os.path.exists('run_simple_line_bot.py'):
        print("🚀 啟動簡化版 Line Bot...")
        run_command("python run_simple_line_bot.py", "啟動 Bot")
    elif os.path.exists('autogen_line_app/simple_line_bot.py'):
        print("🚀 直接啟動 simple_line_bot.py...")
        run_command("python autogen_line_app/simple_line_bot.py", "啟動 Bot")
    else:
        print("❌ 找不到 Bot 啟動文件")
        return False
    
    return True

def main():
    """主程式"""
    print_header()
    
    try:
        # 檢查 Python
        if not check_python():
            input("\n按 Enter 鍵退出...")
            return
        
        # 安裝套件
        install_packages()
        
        # 檢查環境
        env_ok = check_environment()
        
        # 測試安裝
        test_installation()
        
        if not env_ok:
            print("\n⚠️ 環境變數未完整設定")
            print("請編輯 .env 文件後重新執行此腳本")
            input("\n按 Enter 鍵退出...")
            return
        
        # 啟動 Bot
        print("\n🎉 準備啟動 Line Bot!")
        choice = input("是否現在啟動? (y/n): ").lower().strip()
        
        if choice in ['y', 'yes', '']:
            start_bot()
        else:
            print("稍後可執行: python run_simple_line_bot.py")
    
    except KeyboardInterrupt:
        print("\n\n👋 安裝已取消")
    except Exception as e:
        print(f"\n❌ 發生錯誤: {e}")
    finally:
        input("\n按 Enter 鍵退出...")

if __name__ == "__main__":
    main()
