#!/usr/bin/env python3
"""
測試 gTTS 語音功能
"""

import os
from dotenv import load_dotenv
from openai import OpenAI
from gtts_voice_module import process_voice_with_gtts, GTTSVoiceModule

# 載入環境變數
load_dotenv()

def test_gtts_basic():
    """測試基本 gTTS 功能"""
    print("🧪 測試基本 gTTS 功能")
    print("=" * 50)
    
    gtts_module = GTTSVoiceModule()
    
    # 測試英語語音生成
    test_text = "Hello! Today we're going to practice English conversation about travel experiences."
    
    try:
        voice_file = gtts_module.text_to_speech(test_text, lang='en', slow=True)
        
        if voice_file:
            print(f"✅ 語音檔案生成成功: {voice_file}")
            print(f"檔案大小: {os.path.getsize(voice_file)} bytes")
            print(f"檔案存在: {os.path.exists(voice_file)}")
        else:
            print("❌ 語音檔案生成失敗")
            
    except Exception as e:
        print(f"❌ gTTS 測試失敗: {e}")
    
    # 清理
    gtts_module.cleanup_temp_files()
    print("\n" + "=" * 50)

def test_voice_feedback_with_gtts():
    """測試完整的語音回饋功能"""
    print("🧪 測試完整的 gTTS 語音回饋功能")
    print("=" * 60)
    
    # 初始化 OpenAI 客戶端
    openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    
    # 測試案例
    test_cases = [
        {
            "text": "What is the topic today?",
            "topic": "旅遊經驗",
            "level": "intermediate",
            "description": "學生詢問今日主題"
        },
        {
            "text": "I love traveling to Japan",
            "topic": "旅遊經驗",
            "level": "intermediate", 
            "description": "學生分享旅遊喜好"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📝 測試案例 {i}: {case['description']}")
        print(f"學生說: \"{case['text']}\"")
        print(f"主題: {case['topic']}")
        print(f"程度: {case['level']}")
        print("-" * 40)
        
        try:
            feedback_text, voice_file = process_voice_with_gtts(
                openai_client,
                case['text'],
                case['topic'], 
                case['level']
            )
            
            print("✅ 回饋生成成功:")
            print(feedback_text)
            
            if voice_file:
                print(f"\n🔊 語音檔案: {voice_file}")
                print(f"檔案大小: {os.path.getsize(voice_file)} bytes")
                print("💡 您可以播放這個 MP3 檔案來聽取 Emma 老師的回應！")
            else:
                print("\n❌ 語音檔案生成失敗")
                
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
        
        print("\n" + "=" * 60)

def test_teacher_voice_response():
    """測試老師語音回應"""
    print("🧪 測試老師語音回應")
    print("=" * 50)
    
    gtts_module = GTTSVoiceModule()
    
    # 模擬老師回應
    teacher_responses = [
        "Today we're discussing travel experiences. Have you been to any interesting places recently?",
        "That's wonderful! Japan is such a beautiful country. What did you enjoy most about your trip?",
        "Great question! Let me tell you about today's topic."
    ]
    
    for i, response in enumerate(teacher_responses, 1):
        print(f"\n📢 老師回應 {i}: {response}")
        
        try:
            voice_file = gtts_module.create_teacher_voice_response(response)
            
            if voice_file:
                print(f"✅ 語音檔案: {voice_file}")
                print(f"檔案大小: {os.path.getsize(voice_file)} bytes")
            else:
                print("❌ 語音生成失敗")
                
        except Exception as e:
            print(f"❌ 錯誤: {e}")
    
    # 清理
    gtts_module.cleanup_temp_files()
    print("\n" + "=" * 50)

if __name__ == "__main__":
    print("🎤 開始測試 gTTS 語音功能")
    print("=" * 60)
    
    # 測試基本功能
    test_gtts_basic()
    
    # 測試老師語音回應
    test_teacher_voice_response()
    
    # 測試完整回饋功能
    test_voice_feedback_with_gtts()
    
    print("\n🎉 gTTS 語音功能測試完成！")
    print("\n📱 功能說明：")
    print("• ✅ gTTS 語音生成正常運作")
    print("• ✅ Emma 老師可以生成語音回應")
    print("• ✅ 語音檔案保存為 MP3 格式")
    print("• ✅ 支援慢速語音適合學習")
    print("\n🔊 注意：生成的 MP3 檔案可以直接播放！")
    print("💡 下一步：整合到 LINE Bot 中發送語音訊息")
