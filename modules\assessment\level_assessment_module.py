#!/usr/bin/env python3
"""
英語分級測驗模組
基於 AutoGen 多代理分析的分級系統實現

功能：
1. 分級測驗問題生成
2. 回答評估和分級判定
3. 個人化學習路徑推薦
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

# 設定日誌
logger = logging.getLogger(__name__)

class EnglishLevel(Enum):
    """英語程度等級"""
    BEGINNER = "beginner"      # A1-A2
    INTERMEDIATE = "intermediate"  # B1-B2
    ADVANCED = "advanced"      # C1-C2

@dataclass
class AssessmentQuestion:
    """分級測驗問題"""
    id: str
    question: str
    type: str  # "introduction", "daily_conversation", "grammar", "vocabulary"
    expected_level: EnglishLevel
    scoring_criteria: Dict[str, int]

@dataclass
class AssessmentResult:
    """分級測驗結果"""
    user_id: str
    level: EnglishLevel
    score: int
    strengths: List[str]
    weaknesses: List[str]
    recommended_topics: List[str]
    assessment_date: datetime

class LevelAssessmentModule:
    """英語分級測驗模組"""
    
    def __init__(self, openai_client):
        """初始化分級模組"""
        self.openai_client = openai_client
        self.assessment_questions = self._load_assessment_questions()
        self.learning_paths = self._load_learning_paths()
    
    def _load_assessment_questions(self) -> List[AssessmentQuestion]:
        """載入分級測驗問題"""
        questions = [
            AssessmentQuestion(
                id="intro_1",
                question="Please introduce yourself in English. Tell me your name, age, and what you like to do.",
                type="introduction",
                expected_level=EnglishLevel.BEGINNER,
                scoring_criteria={
                    "grammar": 25,
                    "vocabulary": 25,
                    "fluency": 25,
                    "content": 25
                }
            ),
            AssessmentQuestion(
                id="daily_1", 
                question="What did you do yesterday? Please describe your day in detail.",
                type="daily_conversation",
                expected_level=EnglishLevel.INTERMEDIATE,
                scoring_criteria={
                    "grammar": 30,
                    "vocabulary": 25,
                    "fluency": 25,
                    "content": 20
                }
            ),
            AssessmentQuestion(
                id="opinion_1",
                question="What do you think about the impact of technology on modern communication? Please explain your opinion with examples.",
                type="opinion",
                expected_level=EnglishLevel.ADVANCED,
                scoring_criteria={
                    "grammar": 25,
                    "vocabulary": 30,
                    "fluency": 20,
                    "content": 25
                }
            )
        ]
        return questions
    
    def _load_learning_paths(self) -> Dict[EnglishLevel, Dict]:
        """載入學習路徑配置"""
        return {
            EnglishLevel.BEGINNER: {
                "topics": [
                    "自我介紹與基本資訊",
                    "日常生活對話",
                    "購物與用餐",
                    "時間與日期",
                    "家庭與朋友"
                ],
                "focus_areas": [
                    "基礎詞彙建立",
                    "簡單句型練習", 
                    "發音基礎",
                    "日常用語"
                ],
                "difficulty": "simple"
            },
            EnglishLevel.INTERMEDIATE: {
                "topics": [
                    "旅遊與交通",
                    "工作與職業",
                    "興趣與娛樂",
                    "健康與運動",
                    "文化與傳統"
                ],
                "focus_areas": [
                    "複合句型練習",
                    "詞彙擴展",
                    "語法精進",
                    "表達流暢度"
                ],
                "difficulty": "moderate"
            },
            EnglishLevel.ADVANCED: {
                "topics": [
                    "商務與專業討論",
                    "社會議題分析",
                    "科技與創新",
                    "環境與永續發展",
                    "國際時事討論"
                ],
                "focus_areas": [
                    "高階詞彙運用",
                    "複雜語法結構",
                    "批判性思考表達",
                    "專業術語使用"
                ],
                "difficulty": "challenging"
            }
        }
    
    def get_assessment_questions(self, num_questions: int = 3) -> List[AssessmentQuestion]:
        """獲取分級測驗問題"""
        # 確保涵蓋不同難度等級
        selected_questions = []
        
        # 每個等級至少選一題
        for level in EnglishLevel:
            level_questions = [q for q in self.assessment_questions if q.expected_level == level]
            if level_questions:
                selected_questions.append(level_questions[0])
        
        return selected_questions[:num_questions]
    
    def evaluate_response(self, question: AssessmentQuestion, user_response: str) -> Dict[str, int]:
        """評估用戶回答"""
        try:
            # 使用 OpenAI 進行智能評估
            evaluation_prompt = f"""
            請評估以下英語回答的品質，針對四個面向給分（0-100分）：
            
            問題：{question.question}
            回答：{user_response}
            
            評分標準：
            1. Grammar (語法正確性)
            2. Vocabulary (詞彙豐富度和準確性)
            3. Fluency (表達流暢度)
            4. Content (內容完整性和邏輯性)
            
            請以 JSON 格式回傳評分：
            {{
                "grammar": 分數,
                "vocabulary": 分數,
                "fluency": 分數,
                "content": 分數,
                "feedback": "具體建議"
            }}
            """
            
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "你是專業的英語評估老師，請客觀公正地評分。"},
                    {"role": "user", "content": evaluation_prompt}
                ],
                max_tokens=500,
                temperature=0.3
            )
            
            # 解析 JSON 回應
            result = json.loads(response.choices[0].message.content.strip())
            return result
            
        except Exception as e:
            logger.error(f"評估回答時發生錯誤: {e}")
            # 返回預設評分
            return {
                "grammar": 50,
                "vocabulary": 50,
                "fluency": 50,
                "content": 50,
                "feedback": "評估系統暫時無法使用，請稍後再試。"
            }
    
    def determine_level(self, evaluation_results: List[Dict]) -> Tuple[EnglishLevel, int]:
        """根據評估結果判定英語程度"""
        total_score = 0
        total_questions = len(evaluation_results)
        
        for result in evaluation_results:
            question_score = (
                result.get("grammar", 0) + 
                result.get("vocabulary", 0) + 
                result.get("fluency", 0) + 
                result.get("content", 0)
            ) / 4
            total_score += question_score
        
        average_score = total_score / total_questions if total_questions > 0 else 0
        
        # 分級邏輯
        if average_score >= 80:
            return EnglishLevel.ADVANCED, int(average_score)
        elif average_score >= 60:
            return EnglishLevel.INTERMEDIATE, int(average_score)
        else:
            return EnglishLevel.BEGINNER, int(average_score)
    
    def generate_learning_recommendations(self, level: EnglishLevel, evaluation_results: List[Dict]) -> Dict:
        """生成個人化學習建議"""
        learning_path = self.learning_paths[level]
        
        # 分析強弱項
        strengths = []
        weaknesses = []
        
        for result in evaluation_results:
            scores = {
                "grammar": result.get("grammar", 0),
                "vocabulary": result.get("vocabulary", 0), 
                "fluency": result.get("fluency", 0),
                "content": result.get("content", 0)
            }
            
            for skill, score in scores.items():
                if score >= 75:
                    if skill not in strengths:
                        strengths.append(skill)
                elif score < 60:
                    if skill not in weaknesses:
                        weaknesses.append(skill)
        
        return {
            "level": level.value,
            "recommended_topics": learning_path["topics"][:3],  # 推薦前3個主題
            "focus_areas": learning_path["focus_areas"],
            "strengths": strengths,
            "weaknesses": weaknesses,
            "difficulty": learning_path["difficulty"]
        }
    
    def conduct_assessment(self, user_id: str, responses: List[Tuple[AssessmentQuestion, str]]) -> AssessmentResult:
        """進行完整的分級測驗"""
        evaluation_results = []
        
        # 評估每個回答
        for question, response in responses:
            evaluation = self.evaluate_response(question, response)
            evaluation_results.append(evaluation)
        
        # 判定等級
        level, score = self.determine_level(evaluation_results)
        
        # 生成學習建議
        recommendations = self.generate_learning_recommendations(level, evaluation_results)
        
        # 創建測驗結果
        result = AssessmentResult(
            user_id=user_id,
            level=level,
            score=score,
            strengths=recommendations["strengths"],
            weaknesses=recommendations["weaknesses"],
            recommended_topics=recommendations["recommended_topics"],
            assessment_date=datetime.now()
        )
        
        return result
