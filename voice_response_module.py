#!/usr/bin/env python3
"""
語音回覆模組
為英語老師生成語音回覆（文字轉語音）

注意：LINE Bot 目前不支援直接發送語音訊息
這個模組為未來的語音回覆功能做準備
"""

import os
import logging
from typing import Optional
import tempfile

# 設定日誌
logger = logging.getLogger(__name__)

class VoiceResponseModule:
    """語音回覆模組"""
    
    def __init__(self, openai_client):
        """初始化語音回覆模組"""
        self.openai_client = openai_client
    
    def generate_voice_response(self, text_response: str) -> Optional[str]:
        """
        生成語音回覆檔案
        
        Args:
            text_response: 要轉換成語音的文字
            
        Returns:
            語音檔案路徑，如果失敗則返回 None
        """
        try:
            # 使用 OpenAI TTS API 生成語音
            response = self.openai_client.audio.speech.create(
                model="tts-1",
                voice="nova",  # 使用 nova 聲音（女性，適合英語老師）
                input=text_response,
                speed=0.9  # 稍微慢一點，適合學習
            )
            
            # 保存到臨時檔案
            with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as temp_file:
                response.stream_to_file(temp_file.name)
                return temp_file.name
                
        except Exception as e:
            logger.error(f"語音生成失敗: {e}")
            return None
    
    def get_voice_response_info(self, teacher_response: str) -> str:
        """
        獲取語音回覆資訊（當無法發送語音時的替代方案）
        
        Args:
            teacher_response: 老師的文字回應
            
        Returns:
            包含語音資訊的文字訊息
        """
        return f"""
🔊 **Emma 老師的語音回覆：**
"{teacher_response}"

📱 **語音功能說明：**
• LINE Bot 目前不支援發送語音訊息
• 您可以複製上面的文字來練習發音
• 建議使用 Google 翻譯等工具聽取發音
• 未來我們會加入語音回覆功能

💡 **發音練習建議：**
• 跟著文字慢慢朗讀
• 注意語調和重音
• 可以錄製自己的發音來比較
        """.strip()

class EnhancedVoiceFeedback:
    """增強版語音回饋（包含語音回覆）"""
    
    def __init__(self, openai_client):
        """初始化增強版語音回饋"""
        self.openai_client = openai_client
        self.voice_response = VoiceResponseModule(openai_client)
    
    def generate_enhanced_feedback(self, transcribed_text: str, topic: str, user_level: str) -> str:
        """生成包含語音回覆的完整回饋"""
        try:
            # 生成中文翻譯
            translation = self._get_translation(transcribed_text)
            
            # 生成英文老師的回應
            teacher_response = self._get_teacher_response(transcribed_text, topic, user_level)
            
            # 生成語音回覆資訊
            voice_info = self.voice_response.get_voice_response_info(teacher_response)
            
            # 生成學習建議
            improvement = self._get_improvement_suggestion(transcribed_text, topic, user_level)
            
            # 格式化完整回饋
            feedback_message = f"""
🎤 **您說的話：**
"{transcribed_text}"

📝 **中文翻譯：**
{translation}

{voice_info}

✨ **學習建議：**
{improvement}

---

🎯 **繼續對話：**
• 回應老師的問題
• 分享更多想法
• 練習相關詞彙

💪 Keep practicing! You're doing great! 😊
            """.strip()
            
            return feedback_message
            
        except Exception as e:
            logger.error(f"生成增強回饋失敗: {e}")
            return self._get_fallback_feedback(transcribed_text)
    
    def _get_translation(self, text: str) -> str:
        """獲取中文翻譯"""
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "請將英文翻譯成自然的中文。"},
                    {"role": "user", "content": f"翻譯：{text}"}
                ],
                max_tokens=150,
                temperature=0.1
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"翻譯失敗: {e}")
            return "翻譯暫時無法使用，但您的英語表達很不錯！"
    
    def _get_teacher_response(self, text: str, topic: str, level: str) -> str:
        """生成英文老師的回應"""
        try:
            prompt = f"""
            你是 Emma，一位友善的英語老師。學生剛剛對你說："{text}"
            
            當前主題是：{topic}
            學生程度：{level}
            
            請像真正的英語老師一樣回應學生：
            1. 直接回答學生的問題（如果有問題的話）
            2. 針對主題給出相關的回應
            3. 鼓勵學生繼續對話
            4. 可以問一個相關的問題來延續對話
            
            請用自然的英語回應，控制在 2-3 句話內。
            """
            
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "你是 Emma，一位專業且友善的英語老師，擅長與學生進行自然對話。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=200,
                temperature=0.7
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"老師回應生成失敗: {e}")
            return "That's great! I'm here to help you practice English. What would you like to talk about?"
    
    def _get_improvement_suggestion(self, text: str, topic: str, level: str) -> str:
        """獲取改進建議"""
        try:
            prompt = f"""
            學習者說了："{text}"
            主題是：{topic}
            程度：{level}
            
            請給出簡短的學習建議（1-2句話），包括：
            1. 鼓勵的話
            2. 一個具體的改進建議
            
            請用友善鼓勵的語氣。
            """
            
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "你是友善的英語老師，給出簡短有用的建議。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=150,
                temperature=0.5
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"建議生成失敗: {e}")
            return "很好的嘗試！繼續練習會讓您的英語更流利。"
    
    def _get_fallback_feedback(self, text: str) -> str:
        """備用回饋訊息"""
        return f"""
🎤 **您說的話：**
"{text}"

👩‍🏫 **Emma 老師回應：**
That's wonderful! Thank you for practicing with me. Keep up the great work!

📝 **回饋：**
很棒的語音練習！您的發音很清楚。

✨ **建議：**
• 繼續保持練習的習慣
• 嘗試使用更多詞彙
• 表達更完整的想法

💪 繼續加油，您做得很好！
        """.strip()

# 整合函數
def process_voice_enhanced(openai_client, transcribed_text: str, topic: str, user_level: str) -> str:
    """增強版語音處理函數"""
    try:
        feedback_processor = EnhancedVoiceFeedback(openai_client)
        return feedback_processor.generate_enhanced_feedback(transcribed_text, topic, user_level)
    except Exception as e:
        logger.error(f"增強語音處理失敗: {e}")
        return f"""
🎤 **您說的話：**
"{transcribed_text}"

👩‍🏫 **Emma 老師回應：**
Thank you for practicing with me! That was great!

📝 **狀態：**
語音識別成功！回饋系統正在優化中。

💪 **鼓勵：**
很棒的練習！請繼續保持學習的熱忱。

繼續加油！ 😊
        """.strip()
