#!/usr/bin/env python3
"""
AutoGen 多代理調試系統
使用三個代理協作解決 LINE Bot OpenAI 集成問題

Agent1: 程式碼開發者 (Coder Agent)
Agent2: 程式碼驗證者 (Reviewer Agent) 
Agent3: 程式碼優化者 (Optimizer Agent)
"""

import asyncio
import os
from typing import List, Dict, Any
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

# AutoGen 新版本導入
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient

class AutoGenDebugSystem:
    """AutoGen 多代理調試系統"""
    
    def __init__(self):
        """初始化系統"""
        self.model_client = OpenAIChatCompletionClient(
            model="gpt-4o",
            api_key=os.getenv('OPENAI_API_KEY')
        )
        
        # 初始化三個代理
        self.coder_agent = self._create_coder_agent()
        self.reviewer_agent = self._create_reviewer_agent()
        self.optimizer_agent = self._create_optimizer_agent()
        
        # 創建團隊
        self.team = RoundRobinGroupChat([
            self.coder_agent,
            self.reviewer_agent, 
            self.optimizer_agent
        ])
    
    def _create_coder_agent(self) -> AssistantAgent:
        """創建程式碼開發代理"""
        system_message = """
        你是 Agent1 - 程式碼開發專家。你的職責是：
        
        1. 分析 LINE Bot OpenAI 集成問題
        2. 編寫修正的程式碼
        3. 使用最新的 OpenAI Python 客戶端語法
        4. 確保異步處理正確實現
        5. 提供完整的錯誤處理
        
        重點關注：
        - 使用 openai.AsyncOpenAI() 客戶端
        - 正確的 await 語法
        - Line Bot SDK 同步/異步兼容性
        - 環境變數配置
        
        請提供具體的程式碼修正方案。
        """
        
        return AssistantAgent(
            name="coder_agent",
            model_client=self.model_client,
            system_message=system_message,
            reflect_on_tool_use=True
        )
    
    def _create_reviewer_agent(self) -> AssistantAgent:
        """創建程式碼驗證代理"""
        system_message = """
        你是 Agent2 - 程式碼驗證專家。你的職責是：
        
        1. 審查 Agent1 提供的程式碼
        2. 識別潛在問題和錯誤
        3. 檢查最佳實踐遵循情況
        4. 提出具體的改進建議
        5. 驗證 OpenAI API 調用正確性
        
        檢查重點：
        - OpenAI 客戶端初始化
        - 異步函數處理
        - 錯誤處理機制
        - Line Bot 回應格式
        - 性能和安全性
        
        請提供詳細的審查報告和修改建議。
        """
        
        return AssistantAgent(
            name="reviewer_agent", 
            model_client=self.model_client,
            system_message=system_message,
            reflect_on_tool_use=True
        )
    
    def _create_optimizer_agent(self) -> AssistantAgent:
        """創建程式碼優化代理"""
        system_message = """
        你是 Agent3 - 程式碼優化專家。你的職責是：
        
        1. 整合 Agent1 的程式碼和 Agent2 的建議
        2. 優化程式碼結構和性能
        3. 確保程式碼穩定性和可維護性
        4. 提供最終的生產就緒程式碼
        5. 添加適當的日誌和監控
        
        優化重點：
        - 程式碼結構優化
        - 錯誤處理增強
        - 性能優化
        - 可讀性提升
        - 最佳實踐應用
        
        請提供最終優化的完整程式碼解決方案。
        """
        
        return AssistantAgent(
            name="optimizer_agent",
            model_client=self.model_client, 
            system_message=system_message,
            reflect_on_tool_use=True
        )
    
    async def debug_line_bot_issue(self, problem_description: str) -> str:
        """使用多代理系統調試 LINE Bot 問題"""
        
        task = f"""
        LINE Bot OpenAI 集成問題調試：
        
        問題描述：
        {problem_description}
        
        當前錯誤：
        - LINE Bot 可以接收訊息
        - /start 命令正常工作
        - 但 AI 回應功能失敗，返回 "Sorry, I'm having some technical difficulties"
        - OpenAI API 調用出現問題
        
        現有程式碼問題：
        1. OpenAI 客戶端初始化可能不正確
        2. 異步/同步處理混合問題
        3. Line Bot SDK 與 OpenAI 異步調用兼容性
        
        請三個代理協作解決這個問題：
        1. Agent1 提供修正程式碼
        2. Agent2 驗證並提出改進建議  
        3. Agent3 提供最終優化解決方案
        """
        
        # 運行團隊協作
        result = await self.team.run(task=task)
        return result
    
    async def close(self):
        """關閉模型客戶端連接"""
        await self.model_client.close()

async def main():
    """主函數"""
    print("🚀 啟動 AutoGen 多代理調試系統")
    print("=" * 50)
    
    # 初始化系統
    debug_system = AutoGenDebugSystem()
    
    # 問題描述
    problem_description = """
    LINE Bot 與 OpenAI 集成問題：
    
    症狀：
    - Bot 能接收 LINE 訊息
    - /start 命令正常顯示歡迎訊息
    - 但當用戶發送普通訊息時，AI 回應失敗
    - 返回錯誤訊息："Sorry, I'm having some technical difficulties. Let's try again! 😊"
    
    技術環境：
    - Python 3.x
    - FastAPI + uvicorn
    - Line Bot SDK v3
    - OpenAI Python 客戶端
    - 異步處理環境
    
    需要解決的核心問題：
    1. OpenAI API 調用失敗
    2. 異步函數在同步 Line handler 中的正確調用
    3. 錯誤處理和日誌記錄改進
    """
    
    try:
        # 運行多代理調試
        print("🤖 開始多代理協作調試...")
        result = await debug_system.debug_line_bot_issue(problem_description)
        
        print("\n" + "=" * 50)
        print("🎯 調試結果：")
        print("=" * 50)
        print(result)
        
    except Exception as e:
        print(f"❌ 調試過程出錯: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 關閉連接
        await debug_system.close()
        print("\n✅ AutoGen 調試系統已關閉")

if __name__ == "__main__":
    # 運行主函數
    asyncio.run(main())
