#!/usr/bin/env python3
"""
AutoGen 多代理英語分級系統開發
功能：課程導入與分級測驗

Agent1: 分級邏輯開發者 (Level Assessment Developer)
Agent2: 代碼驗證者 (Code Validator)
Agent3: 系統優化者 (System Optimizer)
"""

import asyncio
import os
from typing import List, Dict, Any
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

# AutoGen 導入
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient

class LevelAssessmentAutoGenSystem:
    """AutoGen 英語分級系統開發"""
    
    def __init__(self):
        """初始化系統"""
        self.model_client = OpenAIChatCompletionClient(
            model="gpt-4o",
            api_key=os.getenv('OPENAI_API_KEY')
        )
        
        # 初始化三個代理
        self.developer_agent = self._create_developer_agent()
        self.validator_agent = self._create_validator_agent()
        self.optimizer_agent = self._create_optimizer_agent()
        
        # 創建團隊
        self.team = RoundRobinGroupChat([
            self.developer_agent,
            self.validator_agent,
            self.optimizer_agent
        ])
    
    def _create_developer_agent(self) -> AssistantAgent:
        """創建分級邏輯開發代理"""
        system_message = """
        你是 Agent1 - 英語分級邏輯開發專家。你的職責是：
        
        1. 設計英語分級測驗系統架構
        2. 開發分級測驗問題生成邏輯
        3. 實現回答評估和分級判定算法
        4. 設計個人化學習路徑推薦系統
        
        分級系統需求：
        - 初級 (Beginner): A1-A2 程度
        - 中級 (Intermediate): B1-B2 程度  
        - 高級 (Advanced): C1-C2 程度
        
        測驗內容包括：
        - 自我介紹評估
        - 日常對話問答
        - 語法和詞彙使用
        - 表達流暢度
        
        請提供完整的 Python 代碼實現。
        """
        
        return AssistantAgent(
            name="developer_agent",
            model_client=self.model_client,
            system_message=system_message,
            reflect_on_tool_use=True
        )
    
    def _create_validator_agent(self) -> AssistantAgent:
        """創建代碼驗證代理"""
        system_message = """
        你是 Agent2 - 代碼驗證專家。你的職責是：
        
        1. 審查 Agent1 提供的分級系統代碼
        2. 驗證分級邏輯的準確性和公平性
        3. 檢查代碼的穩定性和錯誤處理
        4. 確保與 LINE Bot 的兼容性
        5. 驗證個人化推薦的合理性
        
        驗證重點：
        - 分級標準是否科學合理
        - 測驗問題是否涵蓋各個層面
        - 評分算法是否公正
        - 代碼是否健壯
        - 用戶體驗是否友好
        
        請提供詳細的審查報告和改進建議。
        """
        
        return AssistantAgent(
            name="validator_agent",
            model_client=self.model_client,
            system_message=system_message,
            reflect_on_tool_use=True
        )
    
    def _create_optimizer_agent(self) -> AssistantAgent:
        """創建系統優化代理"""
        system_message = """
        你是 Agent3 - 系統優化專家。你的職責是：
        
        1. 整合 Agent1 的代碼和 Agent2 的建議
        2. 優化分級系統的性能和用戶體驗
        3. 設計模組化的代碼結構
        4. 實現與現有 LINE Bot 的無縫整合
        5. 提供生產就緒的最終解決方案
        
        優化重點：
        - 代碼模組化和可維護性
        - 分級測驗的互動體驗
        - 個人化推薦的精準度
        - 系統性能和響應速度
        - 未來功能擴展的靈活性
        
        請提供最終優化的完整解決方案。
        """
        
        return AssistantAgent(
            name="optimizer_agent",
            model_client=self.model_client,
            system_message=system_message,
            reflect_on_tool_use=True
        )
    
    async def develop_level_assessment_system(self) -> str:
        """開發英語分級系統"""
        
        task = """
        開發 LINE Bot 英語學習分級系統
        
        功能需求：
        1. 課程導入與分級測驗
           - 學員首次使用時進行簡短分級測驗
           - 包含自我介紹、日常問題回答
           - AI 根據表現分級：初級、中級、高級
        
        2. 個人化學習路徑
           - 根據分級結果推薦適合的對話主題
           - 提供個人化的練習內容
           - 動態調整學習難度
        
        技術要求：
        - 與現有 line_bot_autogen_fixed.py 整合
        - 使用 OpenAI GPT 進行智能評估
        - 支援中英文混合互動
        - 數據持久化存儲
        - 模組化設計便於擴展
        
        用戶流程：
        1. 新用戶發送 /start 觸發分級測驗
        2. 系統引導用戶完成 3-5 個測驗問題
        3. AI 分析回答並給出分級結果
        4. 推薦個人化學習內容和主題
        5. 開始正式的英語學習對話
        
        請三個代理協作開發：
        1. Agent1 設計並實現分級邏輯
        2. Agent2 驗證代碼並提出改進建議
        3. Agent3 提供最終優化的完整解決方案
        """
        
        # 運行團隊協作
        result = await self.team.run(task=task)
        return result
    
    async def close(self):
        """關閉模型客戶端連接"""
        await self.model_client.close()

async def main():
    """主函數"""
    print("🚀 啟動 AutoGen 英語分級系統開發")
    print("=" * 50)
    
    # 初始化系統
    assessment_system = LevelAssessmentAutoGenSystem()
    
    try:
        # 運行多代理開發
        print("🤖 開始多代理協作開發分級系統...")
        result = await assessment_system.develop_level_assessment_system()
        
        print("\n" + "=" * 50)
        print("🎯 開發結果：")
        print("=" * 50)
        print(result)
        
    except Exception as e:
        print(f"❌ 開發過程出錯: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 關閉連接
        await assessment_system.close()
        print("\n✅ AutoGen 分級系統開發完成")

if __name__ == "__main__":
    # 運行主函數
    asyncio.run(main())
