#!/usr/bin/env python3
"""
重新組織 mylineenglishbot 專案結構
將檔案分類到不同模組目錄
"""

import os
import shutil
from pathlib import Path

def create_directory_structure():
    """創建新的目錄結構"""
    
    print("📁 創建新的目錄結構...")
    
    # 定義目錄結構
    directories = [
        "modules",
        "modules/assessment",           # 課程導入與分級
        "modules/voice_conversation",   # 主題式語音對話模組
        "modules/topic_management",     # 主題管理
        "modules/audio_processing",     # 音檔處理
        "core",                        # 核心功能
        "config",                      # 配置檔案
        "temp",                        # 暫存測試檔案
        "static",                      # 靜態檔案 (保持現有)
        "static/audio",               # 音檔目錄 (保持現有)
        "docs",                       # 文檔
        "scripts"                     # 工具腳本
    ]
    
    # 創建目錄
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ 創建目錄: {directory}")

def organize_files():
    """組織檔案到對應目錄"""
    
    print("\n📋 組織檔案...")
    
    # 檔案分類映射
    file_mappings = {
        # 課程導入與分級模組
        "modules/assessment": [
            "level_assessment_module.py",
            "assessment_integration.py",
            "autogen_level_assessment_system.py"
        ],
        
        # 主題式語音對話模組
        "modules/voice_conversation": [
            "voice_conversation_module.py",
            "voice_feedback_simple.py",
            "voice_response_module.py",
            "autogen_voice_conversation_system.py"
        ],
        
        # 主題管理模組
        "modules/topic_management": [
            "topic_management_module.py"
        ],
        
        # 音檔處理模組
        "modules/audio_processing": [
            "gtts_voice_module.py",
            "static_audio_server.py"
        ],
        
        # 核心功能
        "core": [
            "line_bot_autogen_fixed.py"
        ],
        
        # 配置檔案
        "config": [
            "LineBotChannelKeys.txt",
            "https_config.txt",
            "requirements.txt"
        ],
        
        # 文檔
        "docs": [
            "README.md",
            "setup_line_webhook.md"
        ],
        
        # 工具腳本
        "scripts": [
            "nggo.bat",
            "setup_https_voice.py",
            "reorganize_project.py"
        ],
        
        # 測試檔案 (移到 temp)
        "temp": [
            "test_audio_files.py",
            "test_complete_gtts.py",
            "test_enhanced_voice.py",
            "test_gtts_voice.py",
            "test_no_assessment.py",
            "test_voice_download.py",
            "test_voice_feedback.py",
            "test_whisper_integration.py",
            "debug_voice_handler.py",
            "monitor_voice_logs.py"
        ]
    }
    
    # 移動檔案
    for target_dir, files in file_mappings.items():
        for file_name in files:
            if os.path.exists(file_name):
                try:
                    shutil.move(file_name, os.path.join(target_dir, file_name))
                    print(f"✅ 移動: {file_name} → {target_dir}/")
                except Exception as e:
                    print(f"❌ 移動失敗: {file_name} - {e}")
            else:
                print(f"⚠️  檔案不存在: {file_name}")

def create_module_init_files():
    """創建模組 __init__.py 檔案"""
    
    print("\n📝 創建模組初始化檔案...")
    
    # 模組初始化檔案內容
    init_files = {
        "modules/__init__.py": '"""mylineenglishbot 模組包"""',
        
        "modules/assessment/__init__.py": '''"""
課程導入與分級模組
包含英語程度評估、分級測驗等功能
"""

from .level_assessment_module import LevelAssessmentModule
from .assessment_integration import AssessmentIntegration

__all__ = ['LevelAssessmentModule', 'AssessmentIntegration']
''',
        
        "modules/voice_conversation/__init__.py": '''"""
主題式語音對話模組
包含語音識別、對話生成、語音回饋等功能
"""

from .voice_conversation_module import VoiceConversationModule

__all__ = ['VoiceConversationModule']
''',
        
        "modules/topic_management/__init__.py": '''"""
主題管理模組
包含每日主題、學習進度追蹤等功能
"""

from .topic_management_module import TopicManagementModule

__all__ = ['TopicManagementModule']
''',
        
        "modules/audio_processing/__init__.py": '''"""
音檔處理模組
包含語音合成、音檔服務等功能
"""

from .gtts_voice_module import GTTSVoiceModule
from .static_audio_server import init_audio_server, process_voice_with_static_server

__all__ = ['GTTSVoiceModule', 'init_audio_server', 'process_voice_with_static_server']
'''
    }
    
    # 創建初始化檔案
    for file_path, content in init_files.items():
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 創建: {file_path}")

def create_main_entry_point():
    """創建主要入口點"""
    
    print("\n🚀 創建主要入口點...")
    
    main_content = '''#!/usr/bin/env python3
"""
mylineenglishbot 主要入口點
Emma 英語學習 LINE Bot
"""

import sys
import os

# 添加模組路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 導入核心功能
from core.line_bot_autogen_fixed import main

if __name__ == "__main__":
    print("🎤 啟動 Emma 英語學習 LINE Bot")
    print("=" * 50)
    main()
'''
    
    with open("main.py", 'w', encoding='utf-8') as f:
        f.write(main_content)
    
    print("✅ 創建: main.py")

def create_project_readme():
    """創建專案說明檔案"""
    
    print("\n📖 創建專案說明...")
    
    readme_content = '''# mylineenglishbot - Emma 英語學習 LINE Bot

🎤 一個功能完整的 AI 英語學習 LINE Bot，具備語音對話、程度評估、主題式學習等功能。

## 📁 專案結構

```
mylineenglishbot/
├── main.py                    # 主要入口點
├── core/                      # 核心功能
│   └── line_bot_autogen_fixed.py
├── modules/                   # 功能模組
│   ├── assessment/           # 課程導入與分級
│   │   ├── level_assessment_module.py
│   │   ├── assessment_integration.py
│   │   └── autogen_level_assessment_system.py
│   ├── voice_conversation/   # 主題式語音對話
│   │   ├── voice_conversation_module.py
│   │   ├── voice_feedback_simple.py
│   │   ├── voice_response_module.py
│   │   └── autogen_voice_conversation_system.py
│   ├── topic_management/     # 主題管理
│   │   └── topic_management_module.py
│   └── audio_processing/     # 音檔處理
│       ├── gtts_voice_module.py
│       └── static_audio_server.py
├── config/                   # 配置檔案
│   ├── LineBotChannelKeys.txt
│   ├── https_config.txt
│   └── requirements.txt
├── static/                   # 靜態檔案
│   └── audio/               # 語音檔案
├── scripts/                 # 工具腳本
├── docs/                    # 文檔
├── temp/                    # 測試檔案
└── __pycache__/            # Python 快取
```

## 🚀 快速開始

1. 安裝依賴：
   ```bash
   pip install -r config/requirements.txt
   ```

2. 設定環境變數：
   - 複製 `config/LineBotChannelKeys.txt` 並填入您的 LINE Bot 金鑰
   - 設定 OpenAI API 金鑰

3. 啟動服務：
   ```bash
   python main.py
   ```

4. 設定 ngrok (用於 HTTPS)：
   ```bash
   ngrok http 5002
   ```

## 🎯 主要功能

### 1. 課程導入與分級 (modules/assessment/)
- 英語程度評估測驗
- 自動分級系統
- 個人化學習路徑

### 2. 主題式語音對話 (modules/voice_conversation/)
- Whisper API 語音轉文字
- GPT AI 對話生成
- gTTS 語音回覆
- 即時語音回饋

### 3. 主題管理 (modules/topic_management/)
- 每日學習主題
- 進度追蹤
- 個人化推薦

### 4. 音檔處理 (modules/audio_processing/)
- 語音合成 (gTTS)
- 靜態檔案服務
- HTTPS 音檔存取

## 🔧 技術架構

- **LINE Bot SDK v3** - LINE 訊息處理
- **OpenAI API** - Whisper 語音轉文字 + GPT 對話
- **gTTS** - Google Text-to-Speech 語音合成
- **FastAPI** - Web 服務框架
- **ngrok** - HTTPS 隧道服務

## 👩‍🏫 Emma 老師功能

Emma 是您的 AI 英語老師，具備：
- 🎤 語音對話能力
- 📝 即時回饋與建議
- 🎯 主題式教學
- 📊 學習進度追蹤
- 🔊 標準英語發音

## 📱 使用方式

1. 加入 LINE Bot 好友
2. 發送 `/start` 開始使用
3. 完成英語程度評估
4. 開始語音對話練習
5. 接收個人化學習建議

---

**讓 Emma 老師陪您一起學英語！** 🎤👩‍🏫✨
'''
    
    with open("PROJECT_README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 創建: PROJECT_README.md")

def show_reorganization_summary():
    """顯示重組摘要"""
    
    print("\n" + "=" * 60)
    print("🎉 專案重組完成！")
    print("=" * 60)
    
    print("\n📁 新的目錄結構：")
    print("├── main.py                    # 主要入口點")
    print("├── core/                      # 核心功能")
    print("├── modules/                   # 功能模組")
    print("│   ├── assessment/           # 課程導入與分級")
    print("│   ├── voice_conversation/   # 主題式語音對話")
    print("│   ├── topic_management/     # 主題管理")
    print("│   └── audio_processing/     # 音檔處理")
    print("├── config/                   # 配置檔案")
    print("├── static/                   # 靜態檔案")
    print("├── scripts/                  # 工具腳本")
    print("├── docs/                     # 文檔")
    print("└── temp/                     # 測試檔案")
    
    print("\n🚀 使用方式：")
    print("1. 執行: python main.py")
    print("2. 或直接執行: python core/line_bot_autogen_fixed.py")
    
    print("\n💡 優點：")
    print("✅ 模組化架構，易於維護")
    print("✅ 清晰的功能分離")
    print("✅ 測試檔案已整理到 temp/")
    print("✅ 完整的專案文檔")

if __name__ == "__main__":
    print("🔧 mylineenglishbot 專案重組工具")
    print("=" * 60)
    
    # 執行重組步驟
    create_directory_structure()
    organize_files()
    create_module_init_files()
    create_main_entry_point()
    create_project_readme()
    show_reorganization_summary()
    
    print("\n✨ 重組完成！專案結構已優化。")
