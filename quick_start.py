#!/usr/bin/env python3
"""
Emma 英語學習 LINE Bot 快速啟動腳本
自動檢查端口並啟動服務
"""

import os
import sys
import socket
import subprocess
from dotenv import load_dotenv

def check_port(port):
    """檢查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex(('localhost', port))
            return result != 0  # 如果連接失敗，表示端口可用
    except OSError:
        return True  # 如果出錯，假設端口可用

def find_available_port(start_port=5002, max_attempts=10):
    """找到可用的端口"""
    for port in range(start_port, start_port + max_attempts):
        if check_port(port):
            return port
    return None

def kill_process_on_port(port):
    """嘗試停止占用端口的程序"""
    try:
        # Windows 命令
        result = subprocess.run(
            f'netstat -ano | findstr :{port}',
            shell=True, capture_output=True, text=True
        )
        
        if result.stdout:
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        print(f"🔄 嘗試停止程序 PID: {pid}")
                        subprocess.run(f'taskkill /PID {pid} /F', shell=True)
                        return True
    except Exception as e:
        print(f"⚠️ 無法停止程序: {e}")
    return False

def start_bot():
    """啟動 LINE Bot"""
    print("🎤 Emma 英語學習 LINE Bot 快速啟動")
    print("=" * 50)
    
    # 載入環境變數
    load_dotenv()
    
    # 檢查環境變數
    required_vars = ["LINE_CHANNEL_ACCESS_TOKEN", "LINE_CHANNEL_SECRET", "OPENAI_API_KEY"]
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少環境變數: {', '.join(missing_vars)}")
        print("請設定 .env 檔案或環境變數")
        return
    
    print("✅ 環境變數檢查通過")
    
    # 檢查端口
    preferred_port = 5002
    if not check_port(preferred_port):
        print(f"⚠️ 端口 {preferred_port} 被占用")
        
        # 嘗試停止占用的程序
        if kill_process_on_port(preferred_port):
            print(f"✅ 已停止占用端口 {preferred_port} 的程序")
            if check_port(preferred_port):
                port = preferred_port
            else:
                port = find_available_port(5003)
        else:
            port = find_available_port(5003)
    else:
        port = preferred_port
    
    if not port:
        print("❌ 找不到可用的端口")
        return
    
    print(f"🚀 使用端口: {port}")
    
    # 啟動服務
    try:
        from fastapi import FastAPI, Request, HTTPException
        from fastapi.responses import JSONResponse
        from fastapi.staticfiles import StaticFiles
        from linebot.v3 import WebhookHandler
        from linebot.v3.messaging import Configuration, ApiClient, MessagingApi
        from linebot.v3.webhooks import MessageEvent, TextMessageContent, AudioMessageContent
        from linebot.v3.messaging import ReplyMessageRequest, TextMessage
        from linebot.v3.exceptions import InvalidSignatureError
        from openai import OpenAI
        import uvicorn
        from datetime import datetime
        import logging
        
        # 設定日誌
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
        
        # 初始化
        line_config = Configuration(access_token=os.getenv('LINE_CHANNEL_ACCESS_TOKEN'))
        handler = WebhookHandler(os.getenv('LINE_CHANNEL_SECRET'))
        openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        
        # 創建 FastAPI 應用
        app = FastAPI(
            title="Emma English Learning Bot",
            description="重組後的英語學習 LINE Bot",
            version="3.0.0"
        )
        
        # 靜態檔案服務
        os.makedirs("static/audio", exist_ok=True)
        app.mount("/audio", StaticFiles(directory="static/audio"), name="audio")
        
        @app.get("/")
        def root():
            return {
                "message": "Emma English Learning Bot (重組版)",
                "status": "healthy",
                "version": "3.0.0",
                "port": port,
                "timestamp": datetime.now().isoformat()
            }
        
        @app.get("/health")
        def health():
            return {"status": "healthy", "modules": "reorganized", "port": port}
        
        @app.post("/callback")
        async def callback(request: Request):
            signature = request.headers.get('X-Line-Signature', '')
            body = await request.body()
            body_str = body.decode('utf-8')
            
            try:
                handler.handle(body_str, signature)
                return JSONResponse(content={"status": "ok"})
            except InvalidSignatureError:
                raise HTTPException(status_code=400, detail="Invalid signature")
            except Exception as e:
                logger.error(f"Callback 錯誤: {e}")
                raise HTTPException(status_code=500, detail="Internal server error")
        
        @handler.add(MessageEvent, message=TextMessageContent)
        def handle_text_message(event):
            user_message = event.message.text.strip()
            logger.info(f"收到訊息: {user_message}")
            
            if user_message.lower() in ['/start', 'start']:
                response = f"""
🌟 Welcome to Emma English Learning Bot! (重組版)

Hi! I'm Emma, your AI English teacher.

🎯 **服務狀態：**
✅ 專案已重組為模組化結構
✅ 基本文字對話功能正常
✅ 服務運行在端口 {port}
🔄 語音功能整合中...

📁 **新的專案結構：**
• modules/assessment/ - 課程導入與分級
• modules/voice_conversation/ - 主題式語音對話
• modules/topic_management/ - 主題管理
• modules/audio_processing/ - 音檔處理

💡 發送任何英語訊息開始對話！

🔗 **服務 URL：** http://localhost:{port}
                """.strip()
            elif user_message.lower() in ['/help', 'help']:
                response = f"""
📚 **Emma English Learning Bot 使用指南**

🎯 **目前功能：**
✅ 基本 AI 英語對話
✅ 文字訊息處理
✅ 重組後的模組化結構

🔄 **整合中的功能：**
• 語音轉文字 (Whisper API)
• 語音回覆 (gTTS)
• 程度評估
• 主題管理

💬 **可用指令：**
• /start - 查看服務狀態
• /help - 顯示此幫助
• /status - 檢查系統狀態

🌐 **服務資訊：**
• 端口: {port}
• 版本: 3.0.0 (重組版)
• 狀態: 運行中

💡 直接發送英語訊息開始對話練習！
                """.strip()
            elif user_message.lower() in ['/status', 'status']:
                response = f"""
🔧 **系統狀態檢查**

✅ **服務狀態：**
• LINE Bot API: 正常
• OpenAI API: 正常
• FastAPI 服務: 正常
• 端口 {port}: 正常

📁 **模組狀態：**
• 核心功能: ✅ 正常
• 文字對話: ✅ 正常
• 靜態檔案: ✅ 正常
• 語音功能: 🔄 整合中

🎯 **重組進度：**
• 檔案結構: ✅ 完成
• 基本功能: ✅ 完成
• 模組整合: 🔄 進行中
• 語音功能: 🔄 恢復中

💡 系統運行正常，可以進行基本對話！
                """.strip()
            else:
                # AI 對話
                try:
                    ai_response = openai_client.chat.completions.create(
                        model="gpt-3.5-turbo",
                        messages=[
                            {"role": "system", "content": "你是 Emma，一位友善的英語老師。用英語回應學生，保持鼓勵和支持。回應要簡潔明了。"},
                            {"role": "user", "content": user_message}
                        ],
                        max_tokens=200,
                        temperature=0.7
                    )
                    response = ai_response.choices[0].message.content.strip()
                except Exception as e:
                    logger.error(f"AI 回應失敗: {e}")
                    response = "Hello! I'm having some technical difficulties. Let's try again! 😊"
            
            # 發送回覆
            try:
                with ApiClient(line_config) as api_client:
                    line_bot_api = MessagingApi(api_client)
                    line_bot_api.reply_message(
                        ReplyMessageRequest(
                            reply_token=event.reply_token,
                            messages=[TextMessage(text=response)]
                        )
                    )
                logger.info("✅ 回覆發送成功")
            except Exception as e:
                logger.error(f"❌ 發送回覆失敗: {e}")
        
        @handler.add(MessageEvent, message=AudioMessageContent)
        def handle_audio_message(event):
            logger.info("🎤 收到語音訊息")
            
            response = f"""
🎤 **語音功能狀態**

✅ 語音訊息接收正常
🔄 語音處理模組整合中...

📋 **重組進度：**
• 檔案結構已重組 ✅
• 基本服務正常 ✅ (端口 {port})
• 模組化導入調整中 🔄
• 語音功能恢復中 🔄

💡 **暫時建議：**
請使用文字訊息進行對話
語音功能將很快恢復！

🔗 服務運行在: http://localhost:{port}
            """.strip()
            
            try:
                with ApiClient(line_config) as api_client:
                    line_bot_api = MessagingApi(api_client)
                    line_bot_api.reply_message(
                        ReplyMessageRequest(
                            reply_token=event.reply_token,
                            messages=[TextMessage(text=response)]
                        )
                    )
                logger.info("✅ 語音狀態回覆發送成功")
            except Exception as e:
                logger.error(f"❌ 發送語音狀態回覆失敗: {e}")
        
        print("✅ LINE Bot 初始化成功")
        print(f"🌐 服務 URL: http://localhost:{port}")
        print(f"🔗 健康檢查: http://localhost:{port}/health")
        print("🎤 Emma 老師準備就緒！")
        
        # 啟動服務
        uvicorn.run(app, host="0.0.0.0", port=port, log_level="info")
        
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    start_bot()
