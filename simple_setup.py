#!/usr/bin/env python3
"""
簡化版安裝腳本 - 避免編碼問題
"""

import subprocess
import sys
import os
import shutil

def print_step(step, description):
    """顯示步驟"""
    print(f"\n📍 步驟 {step}: {description}")

def run_pip_install(package):
    """安裝套件"""
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", package], 
                      check=True, capture_output=True)
        print(f"✅ {package} 安裝成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package} 安裝失敗")
        return False

def main():
    """主程式"""
    print("🤖 AutoGen Line Bot 簡化安裝")
    print("=" * 40)
    
    # 步驟 1: 檢查 Python
    print_step(1, "檢查 Python 版本")
    version = sys.version_info
    print(f"Python 版本: {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 8):
        print("❌ 需要 Python 3.8+")
        input("按 Enter 退出...")
        return
    
    # 步驟 2: 升級 pip
    print_step(2, "升級 pip")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        print("✅ pip 升級成功")
    except:
        print("⚠️ pip 升級失敗，繼續安裝")
    
    # 步驟 3: 安裝套件
    print_step(3, "安裝必要套件")
    packages = [
        "fastapi",
        "uvicorn",
        "line-bot-sdk",
        "openai",
        "requests",
        "pydantic"
    ]
    
    success_count = 0
    for package in packages:
        if run_pip_install(package):
            success_count += 1
    
    print(f"\n套件安裝結果: {success_count}/{len(packages)}")
    
    # 步驟 4: 檢查環境變數
    print_step(4, "檢查環境設定")
    
    required_vars = ["LINE_CHANNEL_ACCESS_TOKEN", "LINE_CHANNEL_SECRET", "OPENAI_API_KEY"]
    missing_vars = []
    
    for var in required_vars:
        if os.getenv(var):
            print(f"✅ {var} 已設定")
        else:
            print(f"❌ {var} 未設定")
            missing_vars.append(var)
    
    # 步驟 5: 處理 .env 文件
    if missing_vars:
        print_step(5, "設定環境變數")
        
        if not os.path.exists('.env') and os.path.exists('.env.example'):
            shutil.copy('.env.example', '.env')
            print("✅ 已複製 .env.example 為 .env")
        
        if os.path.exists('.env'):
            print("📝 請編輯 .env 文件設定以下變數:")
            for var in missing_vars:
                print(f"  - {var}")
            
            choice = input("\n是否現在打開 .env 文件編輯? (y/n): ").lower()
            if choice in ['y', 'yes']:
                try:
                    if os.name == 'nt':  # Windows
                        os.system('notepad .env')
                    else:
                        os.system('nano .env')
                except:
                    print("請手動編輯 .env 文件")
    
    # 步驟 6: 測試導入
    print_step(6, "測試基本導入")
    
    test_imports = [
        ("fastapi", "FastAPI"),
        ("uvicorn", "Uvicorn"),
        ("linebot", "Line Bot SDK"),
        ("openai", "OpenAI")
    ]
    
    import_success = 0
    for module, name in test_imports:
        try:
            __import__(module)
            print(f"✅ {name} 導入成功")
            import_success += 1
        except ImportError:
            print(f"❌ {name} 導入失敗")
    
    # 總結
    print("\n" + "=" * 40)
    print("📊 安裝總結:")
    print(f"  套件安裝: {success_count}/{len(packages)}")
    print(f"  導入測試: {import_success}/{len(test_imports)}")
    print(f"  環境變數: {len(required_vars) - len(missing_vars)}/{len(required_vars)}")
    
    if success_count >= 4 and import_success >= 3:
        print("\n🎉 安裝基本完成！")
        
        if missing_vars:
            print("⚠️ 請先設定環境變數，然後執行:")
        else:
            print("✅ 可以啟動 Bot:")
        
        print("  python run_simple_line_bot.py")
        
        if not missing_vars:
            choice = input("\n是否現在啟動 Bot? (y/n): ").lower()
            if choice in ['y', 'yes']:
                print("\n🚀 啟動 Line Bot...")
                try:
                    import autogen_line_app.simple_line_bot
                    print("Bot 啟動中...")
                except ImportError:
                    print("請執行: python run_simple_line_bot.py")
                except Exception as e:
                    print(f"啟動錯誤: {e}")
                    print("請檢查環境變數設定")
    else:
        print("\n❌ 安裝未完成，請檢查錯誤訊息")
    
    input("\n按 Enter 鍵退出...")

if __name__ == "__main__":
    main()
