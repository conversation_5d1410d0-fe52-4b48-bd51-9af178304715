#!/usr/bin/env python3
"""
簡化版 Line Bot 啟動腳本
解決導入問題的版本
"""

import os
import sys
import logging
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_environment():
    """檢查環境變數"""
    required_vars = [
        'LINE_CHANNEL_ACCESS_TOKEN',
        'LINE_CHANNEL_SECRET',
        'OPENAI_API_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"❌ 缺少必要的環境變數: {', '.join(missing_vars)}")
        logger.info("請設定以下環境變數:")
        for var in missing_vars:
            logger.info(f"  export {var}='your_value_here'")
        logger.info("或者複製 .env.example 為 .env 並編輯設定")
        return False
    
    return True

def check_dependencies():
    """檢查依賴套件"""
    required_packages = [
        ('fastapi', 'FastAPI'),
        ('uvicorn', 'Uvicorn'),
        ('linebot', 'Line Bot SDK'),
        ('openai', 'OpenAI')
    ]
    
    missing_packages = []
    for package, name in required_packages:
        try:
            __import__(package)
            logger.info(f"✅ {name} 已安裝")
        except ImportError:
            missing_packages.append(name)
            logger.error(f"❌ {name} 未安裝")
    
    if missing_packages:
        logger.error(f"缺少套件: {', '.join(missing_packages)}")
        logger.info("請執行以下命令安裝:")
        logger.info("  python install_dependencies.py")
        logger.info("或者:")
        logger.info("  pip install fastapi uvicorn line-bot-sdk openai")
        return False
    
    return True

def main():
    """主程式"""
    logger.info("🚀 啟動簡化版 Line 英語學習 Bot")

    # 檢查依賴
    if not check_dependencies():
        logger.error("❌ 依賴檢查失敗")
        return

    # 檢查環境
    if not check_environment():
        logger.error("❌ 環境檢查失敗")
        return

    try:
        # 導入並啟動 Bot
        from autogen_line_app.simple_line_bot import SimpleLineBot

        bot = SimpleLineBot()
        logger.info("✅ Bot 初始化完成")

        # 啟動服務
        bot.run(host="0.0.0.0", port=8000)

    except ImportError as e:
        logger.error(f"❌ 導入錯誤: {e}")
        logger.info("請確保已安裝所有依賴")
    except Exception as e:
        logger.error(f"❌ 啟動失敗: {e}")
        raise

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("👋 Bot 已停止")
    except Exception as e:
        logger.error(f"💥 意外錯誤: {e}")
        sys.exit(1)
