#!/usr/bin/env python3
"""
Emma 英語學習 LINE Bot 簡單啟動腳本
使用端口 5003 避免衝突
"""

import os
import sys
from dotenv import load_dotenv

def main():
    print("🎤 Emma 英語學習 LINE Bot")
    print("=" * 40)
    
    # 載入環境變數
    load_dotenv()
    
    # 檢查環境變數
    required_vars = ["LINE_CHANNEL_ACCESS_TOKEN", "LINE_CHANNEL_SECRET", "OPENAI_API_KEY"]
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少環境變數: {', '.join(missing_vars)}")
        print("請設定 .env 檔案")
        return
    
    print("✅ 環境變數檢查通過")
    
    # 使用端口 5003 避免衝突
    port = 5003
    print(f"🚀 使用端口: {port}")
    
    try:
        from fastapi import FastAPI, Request, HTTPException
        from fastapi.responses import JSONResponse
        from fastapi.staticfiles import StaticFiles
        from linebot.v3 import WebhookHandler
        from linebot.v3.messaging import Configuration, ApiClient, MessagingApi
        from linebot.v3.webhooks import MessageEvent, TextMessageContent, AudioMessageContent
        from linebot.v3.messaging import ReplyMessageRequest, TextMessage
        from linebot.v3.exceptions import InvalidSignatureError
        from openai import OpenAI
        import uvicorn
        from datetime import datetime
        import logging
        
        # 設定日誌
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
        
        # 初始化
        line_config = Configuration(access_token=os.getenv('LINE_CHANNEL_ACCESS_TOKEN'))
        handler = WebhookHandler(os.getenv('LINE_CHANNEL_SECRET'))
        openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        
        # 創建 FastAPI 應用
        app = FastAPI(
            title="Emma English Learning Bot",
            description="重組後的英語學習 LINE Bot",
            version="3.0.0"
        )
        
        # 靜態檔案服務
        os.makedirs("static/audio", exist_ok=True)
        app.mount("/audio", StaticFiles(directory="static/audio"), name="audio")
        
        @app.get("/")
        def root():
            return {
                "message": "Emma English Learning Bot (重組版)",
                "status": "healthy",
                "version": "3.0.0",
                "port": port,
                "timestamp": datetime.now().isoformat()
            }
        
        @app.get("/health")
        def health():
            return {"status": "healthy", "modules": "reorganized", "port": port}
        
        @app.post("/callback")
        async def callback(request: Request):
            signature = request.headers.get('X-Line-Signature', '')
            body = await request.body()
            body_str = body.decode('utf-8')
            
            try:
                handler.handle(body_str, signature)
                return JSONResponse(content={"status": "ok"})
            except InvalidSignatureError:
                raise HTTPException(status_code=400, detail="Invalid signature")
            except Exception as e:
                logger.error(f"Callback 錯誤: {e}")
                raise HTTPException(status_code=500, detail="Internal server error")
        
        @handler.add(MessageEvent, message=TextMessageContent)
        def handle_text_message(event):
            user_message = event.message.text.strip()
            logger.info(f"收到訊息: {user_message}")
            
            if user_message.lower() in ['/start', 'start']:
                response = f"""
🌟 Welcome to Emma English Learning Bot!

Hi! I'm Emma, your AI English teacher.

🎯 **服務狀態：**
✅ 重組版本運行正常
✅ 端口 {port} 服務中
✅ 基本對話功能正常

📁 **專案已重組：**
• 模組化結構 ✅
• 檔案分類整理 ✅
• 測試檔案已移至 temp/ ✅

💡 發送英語訊息開始對話！
🔗 服務: http://localhost:{port}
                """.strip()
                
            elif user_message.lower() in ['/help', 'help']:
                response = f"""
📚 **Emma 使用指南**

🎯 **可用功能：**
✅ AI 英語對話
✅ 文字訊息處理
✅ 重組後模組化結構

💬 **指令：**
• /start - 查看狀態
• /help - 顯示幫助
• /info - 系統資訊

🌐 **服務資訊：**
• 端口: {port}
• 版本: 3.0.0 (重組版)

💡 直接發送英語開始對話！
                """.strip()
                
            elif user_message.lower() in ['/info', 'info']:
                response = f"""
🔧 **系統資訊**

📊 **服務狀態：**
• LINE Bot: ✅ 正常
• OpenAI: ✅ 正常
• FastAPI: ✅ 正常
• 端口 {port}: ✅ 正常

📁 **重組狀態：**
• 檔案結構: ✅ 完成
• 模組分類: ✅ 完成
• 基本功能: ✅ 正常
• 語音功能: 🔄 整合中

🎯 **模組結構：**
• assessment/ - 分級評估
• voice_conversation/ - 語音對話
• topic_management/ - 主題管理
• audio_processing/ - 音檔處理

💡 重組完成，結構更清晰！
                """.strip()
            else:
                # AI 對話
                try:
                    ai_response = openai_client.chat.completions.create(
                        model="gpt-3.5-turbo",
                        messages=[
                            {"role": "system", "content": "你是 Emma，一位友善的英語老師。用英語回應學生，保持鼓勵和支持。"},
                            {"role": "user", "content": user_message}
                        ],
                        max_tokens=200,
                        temperature=0.7
                    )
                    response = ai_response.choices[0].message.content.strip()
                except Exception as e:
                    logger.error(f"AI 回應失敗: {e}")
                    response = "Hello! I'm having some technical difficulties. Let's try again! 😊"
            
            # 發送回覆
            try:
                with ApiClient(line_config) as api_client:
                    line_bot_api = MessagingApi(api_client)
                    line_bot_api.reply_message(
                        ReplyMessageRequest(
                            reply_token=event.reply_token,
                            messages=[TextMessage(text=response)]
                        )
                    )
                logger.info("✅ 回覆發送成功")
            except Exception as e:
                logger.error(f"❌ 發送回覆失敗: {e}")
        
        @handler.add(MessageEvent, message=AudioMessageContent)
        def handle_audio_message(event):
            logger.info("🎤 收到語音訊息")
            
            response = f"""
🎤 **語音功能狀態**

✅ 語音訊息接收正常
🔄 語音處理整合中...

📋 **重組進度：**
• 檔案結構: ✅ 完成
• 基本服務: ✅ 正常 (端口 {port})
• 語音功能: 🔄 恢復中

💡 **暫時建議：**
請使用文字訊息進行對話
語音功能將很快恢復！

🔗 http://localhost:{port}
            """.strip()
            
            try:
                with ApiClient(line_config) as api_client:
                    line_bot_api = MessagingApi(api_client)
                    line_bot_api.reply_message(
                        ReplyMessageRequest(
                            reply_token=event.reply_token,
                            messages=[TextMessage(text=response)]
                        )
                    )
                logger.info("✅ 語音狀態回覆發送成功")
            except Exception as e:
                logger.error(f"❌ 發送語音狀態回覆失敗: {e}")
        
        print("✅ Emma 老師初始化成功")
        print(f"🌐 服務 URL: http://localhost:{port}")
        print(f"🔗 健康檢查: http://localhost:{port}/health")
        print("🎤 準備接收訊息...")
        print("\n💡 提示：")
        print(f"• 如果需要更新 LINE Webhook URL，請使用: http://your-ngrok-url.ngrok.io/callback")
        print(f"• 或者如果使用 ngrok: ngrok http {port}")
        
        # 啟動服務
        uvicorn.run(app, host="0.0.0.0", port=port, log_level="info")
        
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
