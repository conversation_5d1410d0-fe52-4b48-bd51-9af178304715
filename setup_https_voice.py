#!/usr/bin/env python3
"""
設置 HTTPS 語音功能
解決 LINE Bot 語音訊息需要 HTTPS URL 的問題
"""

import requests
import json

def check_ngrok_status():
    """檢查 ngrok 狀態"""
    print("🔧 檢查 ngrok 狀態")
    print("=" * 50)
    
    try:
        # ngrok 本地 API
        response = requests.get("http://localhost:4040/api/tunnels", timeout=5)
        
        if response.status_code == 200:
            tunnels = response.json()
            print("✅ ngrok 正在運行")
            
            for tunnel in tunnels.get('tunnels', []):
                name = tunnel.get('name', 'unknown')
                public_url = tunnel.get('public_url', '')
                config = tunnel.get('config', {})
                addr = config.get('addr', '')
                
                print(f"📡 隧道: {name}")
                print(f"   本地地址: {addr}")
                print(f"   公開 URL: {public_url}")
                
                # 檢查是否是我們需要的 5002 端口
                if '5002' in addr and public_url.startswith('https://'):
                    print(f"🎯 找到 HTTPS URL: {public_url}")
                    return public_url
            
            print("⚠️  未找到 5002 端口的 HTTPS 隧道")
            return None
            
        else:
            print(f"❌ ngrok API 回應異常: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 無法連接到 ngrok: {e}")
        print("\n💡 請確認：")
        print("1. ngrok 是否已安裝")
        print("2. ngrok 是否正在運行")
        print("3. 執行命令: ngrok http 5002")
        return None

def create_https_voice_config():
    """創建 HTTPS 語音配置"""
    
    print("\n🔧 創建 HTTPS 語音配置")
    print("=" * 50)
    
    # 檢查 ngrok
    https_url = check_ngrok_status()
    
    if https_url:
        config = f"""
# HTTPS 語音配置
HTTPS_BASE_URL = "{https_url}"
HTTP_BASE_URL = "http://localhost:5002"

# 在 line_bot_autogen_fixed.py 中使用：
# base_url = "{https_url}"  # 替換現有的 base_url
        """
        
        print("✅ HTTPS 配置已準備：")
        print(config)
        
        # 保存配置
        with open("https_config.txt", "w", encoding="utf-8") as f:
            f.write(config)
        
        print(f"📁 配置已保存到: https_config.txt")
        
        return https_url
    else:
        print("❌ 無法獲取 HTTPS URL")
        
        print("\n📋 手動設置步驟：")
        print("1. 開啟新的命令提示字元")
        print("2. 執行: ngrok http 5002")
        print("3. 複製 HTTPS URL (例如: https://abc123.ngrok.io)")
        print("4. 在 line_bot_autogen_fixed.py 中修改 base_url")
        
        return None

def test_https_audio_access(https_url):
    """測試 HTTPS 音檔存取"""
    
    if not https_url:
        return
    
    print(f"\n🧪 測試 HTTPS 音檔存取")
    print("=" * 50)
    
    # 檢查音檔目錄
    import os
    audio_dir = "static/audio"
    
    if os.path.exists(audio_dir):
        files = os.listdir(audio_dir)
        if files:
            # 測試最新的檔案
            latest_file = files[-1]
            test_url = f"{https_url}/audio/{latest_file}"
            
            print(f"測試檔案: {latest_file}")
            print(f"HTTPS URL: {test_url}")
            
            try:
                response = requests.head(test_url, timeout=10)
                if response.status_code == 200:
                    print("✅ HTTPS 音檔存取正常")
                    print(f"檔案類型: {response.headers.get('content-type', 'unknown')}")
                else:
                    print(f"❌ HTTPS 存取失敗: {response.status_code}")
            except Exception as e:
                print(f"❌ 測試失敗: {e}")
        else:
            print("📁 音檔目錄為空，請先發送語音訊息生成檔案")
    else:
        print("❌ 音檔目錄不存在")

def show_line_bot_voice_solution():
    """顯示 LINE Bot 語音解決方案"""
    
    print("\n🎤 LINE Bot 語音功能解決方案")
    print("=" * 60)
    
    print("📋 問題分析：")
    print("• LINE Bot 要求語音檔案 URL 必須是 HTTPS")
    print("• 目前使用 HTTP localhost URL")
    print("• 需要公開可存取的 HTTPS URL")
    
    print("\n✅ 解決方案：")
    print("1. 使用 ngrok 提供 HTTPS 隧道")
    print("2. 修改 base_url 為 ngrok HTTPS URL")
    print("3. 確保靜態檔案服務正常運行")
    
    print("\n🔧 實施步驟：")
    print("1. 執行: ngrok http 5002")
    print("2. 複製 HTTPS URL")
    print("3. 修改 line_bot_autogen_fixed.py 中的 base_url")
    print("4. 重新啟動 LINE Bot")
    print("5. 測試語音功能")
    
    print("\n💡 替代方案：")
    print("• 部署到雲端服務 (Heroku, Railway, etc.)")
    print("• 使用 Cloudflare Tunnel")
    print("• 使用其他 HTTPS 代理服務")

if __name__ == "__main__":
    print("🎤 HTTPS 語音功能設置工具")
    print("=" * 60)
    
    # 檢查 ngrok 並創建配置
    https_url = create_https_voice_config()
    
    # 測試 HTTPS 存取
    test_https_audio_access(https_url)
    
    # 顯示解決方案
    show_line_bot_voice_solution()
    
    if https_url:
        print(f"\n🎉 HTTPS URL 已準備就緒: {https_url}")
        print("\n📝 下一步：")
        print(f"1. 在 line_bot_autogen_fixed.py 中修改：")
        print(f'   base_url = "{https_url}"')
        print("2. 重新啟動 LINE Bot")
        print("3. 發送語音訊息測試")
    else:
        print("\n⚠️  請先設置 ngrok HTTPS 隧道")
        print("執行: ngrok http 5002")
