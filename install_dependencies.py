#!/usr/bin/env python3
"""
安裝 AutoGen Line Bot 所需的依賴套件
"""

import subprocess
import sys
import os

def run_command(command):
    """執行命令並顯示輸出"""
    print(f"執行: {command}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ 成功: {command}")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 失敗: {command}")
        print(f"錯誤: {e.stderr}")
        return False

def install_dependencies():
    """安裝依賴套件"""
    print("🚀 開始安裝 AutoGen Line Bot 依賴套件...")
    
    # 基本套件
    basic_packages = [
        "fastapi==0.104.1",
        "uvicorn[standard]==0.24.0",
        "pydantic==2.5.0",
        "requests",
        "openai",
        "line-bot-sdk==3.7.0"
    ]
    
    # AutoGen 套件
    autogen_packages = [
        "autogen-agentchat==0.4.0",
        "autogen-ext[openai]==0.4.0", 
        "autogen-core==0.4.0"
    ]
    
    # 語音處理套件
    speech_packages = [
        "SpeechRecognition==3.10.0",
        "pydub==0.25.1"
    ]
    
    # 測試套件
    test_packages = [
        "pytest==7.4.3",
        "pytest-asyncio==0.21.1",
        "httpx==0.25.2"
    ]
    
    all_packages = basic_packages + autogen_packages + speech_packages + test_packages
    
    print("📦 安裝基本套件...")
    for package in basic_packages:
        if not run_command(f"pip install {package}"):
            print(f"⚠️ 警告: {package} 安裝失敗")
    
    print("🤖 安裝 AutoGen 套件...")
    for package in autogen_packages:
        if not run_command(f"pip install {package}"):
            print(f"⚠️ 警告: {package} 安裝失敗")
    
    print("🎤 安裝語音處理套件...")
    for package in speech_packages:
        if not run_command(f"pip install {package}"):
            print(f"⚠️ 警告: {package} 安裝失敗，語音功能可能不可用")
    
    print("🧪 安裝測試套件...")
    for package in test_packages:
        if not run_command(f"pip install {package}"):
            print(f"⚠️ 警告: {package} 安裝失敗")
    
    print("✅ 依賴套件安裝完成！")

def check_installation():
    """檢查安裝狀態"""
    print("\n🔍 檢查套件安裝狀態...")
    
    packages_to_check = [
        "fastapi",
        "uvicorn", 
        "pydantic",
        "requests",
        "openai",
        "linebot",
        "autogen_agentchat",
        "autogen_ext",
        "speech_recognition",
        "pydub",
        "pytest"
    ]
    
    for package in packages_to_check:
        try:
            __import__(package)
            print(f"✅ {package} - 已安裝")
        except ImportError:
            print(f"❌ {package} - 未安裝")

def main():
    """主程式"""
    print("🎯 AutoGen Line Bot 依賴安裝工具")
    print("=" * 50)
    
    # 檢查 Python 版本
    if sys.version_info < (3, 8):
        print("❌ 錯誤: 需要 Python 3.8 或更高版本")
        sys.exit(1)
    
    print(f"✅ Python 版本: {sys.version}")
    
    # 升級 pip
    print("\n📈 升級 pip...")
    run_command("python -m pip install --upgrade pip")
    
    # 安裝依賴
    install_dependencies()
    
    # 檢查安裝
    check_installation()
    
    print("\n🎉 安裝完成！")
    print("\n📋 下一步:")
    print("1. 複製 .env.example 為 .env")
    print("2. 設定環境變數 (LINE_CHANNEL_ACCESS_TOKEN, LINE_CHANNEL_SECRET, OPENAI_API_KEY)")
    print("3. 執行: python run_autogen_line_bot.py")

if __name__ == "__main__":
    main()
