#!/usr/bin/env python3
"""
簡化版語音回饋模組
專注於穩定的語音轉文字和基本回饋功能
"""

import logging
from typing import Optional

# 設定日誌
logger = logging.getLogger(__name__)

class SimpleVoiceFeedback:
    """簡化版語音回饋處理器"""
    
    def __init__(self, openai_client):
        """初始化簡化版語音回饋"""
        self.openai_client = openai_client
    
    def generate_simple_feedback(self, transcribed_text: str, topic: str, user_level: str) -> str:
        """生成簡化的語音回饋"""
        try:
            # 生成中文翻譯
            translation = self._get_translation(transcribed_text)

            # 生成英文老師的回應（回答學生的問題）
            teacher_response = self._get_teacher_response(transcribed_text, topic, user_level)

            # 生成優化建議
            improvement = self._get_improvement_suggestion(transcribed_text, topic, user_level)

            # 格式化回饋訊息
            feedback_message = f"""
🎤 **您說的話：**
"{transcribed_text}"

📝 **中文翻譯：**
{translation}

👩‍🏫 **Emma 老師回應：**
{teacher_response}

✨ **學習建議：**
{improvement}

---

🎯 **繼續練習：**
• 嘗試說得更慢更清楚
• 使用更多相關詞彙
• 表達更完整的想法

💪 很棒的嘗試！繼續加油！

🔊 **注意：語音回覆功能開發中，目前提供文字回應**
            """.strip()

            return feedback_message

        except Exception as e:
            logger.error(f"生成簡化回饋失敗: {e}")
            return self._get_fallback_feedback(transcribed_text)
    
    def _get_translation(self, text: str) -> str:
        """獲取中文翻譯"""
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "請將英文翻譯成自然的中文。"},
                    {"role": "user", "content": f"翻譯：{text}"}
                ],
                max_tokens=150,
                temperature=0.1
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"翻譯失敗: {e}")
            return "翻譯暫時無法使用，但您的英語表達很不錯！"
    
    def _get_teacher_response(self, text: str, topic: str, level: str) -> str:
        """生成英文老師的回應"""
        try:
            prompt = f"""
            你是 Emma，一位友善的英語老師。學生剛剛對你說："{text}"

            當前主題是：{topic}
            學生程度：{level}

            請像真正的英語老師一樣回應學生：
            1. 直接回答學生的問題（如果有問題的話）
            2. 針對主題給出相關的回應
            3. 鼓勵學生繼續對話
            4. 可以問一個相關的問題來延續對話

            請用自然的英語回應，就像面對面對話一樣。
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "你是 Emma，一位專業且友善的英語老師，擅長與學生進行自然對話。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=300,
                temperature=0.7
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"老師回應生成失敗: {e}")
            return "That's great! I'm here to help you practice English. What would you like to talk about?"

    def _get_improvement_suggestion(self, text: str, topic: str, level: str) -> str:
        """獲取改進建議"""
        try:
            prompt = f"""
            學習者說了："{text}"
            主題是：{topic}
            程度：{level}

            請給出簡短的學習建議（1-2句話），包括：
            1. 鼓勵的話
            2. 一個具體的改進建議

            請用友善鼓勵的語氣。
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "你是友善的英語老師，給出簡短有用的建議。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=200,
                temperature=0.5
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"建議生成失敗: {e}")
            return "很好的嘗試！繼續練習會讓您的英語更流利。"
    
    def _get_fallback_feedback(self, text: str) -> str:
        """備用回饋訊息"""
        return f"""
🎤 **語音轉文字結果：**
"{text}"

📝 **回饋：**
很棒的語音練習！您的發音很清楚。

✨ **建議：**
• 繼續保持練習的習慣
• 嘗試使用更多詞彙
• 表達更完整的想法

💪 繼續加油，您做得很好！
        """.strip()

# 整合到現有系統的簡化處理函數
def process_voice_simple(openai_client, transcribed_text: str, topic: str, user_level: str) -> str:
    """簡化的語音處理函數"""
    try:
        feedback_processor = SimpleVoiceFeedback(openai_client)
        return feedback_processor.generate_simple_feedback(transcribed_text, topic, user_level)
    except Exception as e:
        logger.error(f"簡化語音處理失敗: {e}")
        return f"""
🎤 **語音轉文字結果：**
"{transcribed_text}"

📝 **狀態：**
語音識別成功！回饋系統正在優化中。

💪 **鼓勵：**
很棒的練習！請繼續保持學習的熱忱。

🎯 **建議：**
• 多練習口說
• 嘗試不同主題
• 保持學習動力

繼續加油！ 😊
        """.strip()
