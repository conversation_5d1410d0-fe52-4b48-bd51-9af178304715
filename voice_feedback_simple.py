#!/usr/bin/env python3
"""
簡化版語音回饋模組
專注於穩定的語音轉文字和基本回饋功能
"""

import logging
from typing import Optional

# 設定日誌
logger = logging.getLogger(__name__)

class SimpleVoiceFeedback:
    """簡化版語音回饋處理器"""
    
    def __init__(self, openai_client):
        """初始化簡化版語音回饋"""
        self.openai_client = openai_client
    
    def generate_simple_feedback(self, transcribed_text: str, topic: str, user_level: str) -> str:
        """生成簡化的語音回饋"""
        try:
            # 生成中文翻譯
            translation = self._get_translation(transcribed_text)
            
            # 生成優化建議
            improvement = self._get_improvement_suggestion(transcribed_text, topic, user_level)
            
            # 格式化回饋訊息
            feedback_message = f"""
🎤 **語音轉文字結果：**
"{transcribed_text}"

📝 **中文翻譯：**
{translation}

✨ **學習建議：**
{improvement}

---

🎯 **繼續練習：**
• 嘗試說得更慢更清楚
• 使用更多相關詞彙
• 表達更完整的想法

💪 很棒的嘗試！繼續加油！
            """.strip()
            
            return feedback_message
            
        except Exception as e:
            logger.error(f"生成簡化回饋失敗: {e}")
            return self._get_fallback_feedback(transcribed_text)
    
    def _get_translation(self, text: str) -> str:
        """獲取中文翻譯"""
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "請將英文翻譯成自然的中文。"},
                    {"role": "user", "content": f"翻譯：{text}"}
                ],
                max_tokens=150,
                temperature=0.1
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"翻譯失敗: {e}")
            return "翻譯暫時無法使用，但您的英語表達很不錯！"
    
    def _get_improvement_suggestion(self, text: str, topic: str, level: str) -> str:
        """獲取改進建議"""
        try:
            prompt = f"""
            學習者說了："{text}"
            主題是：{topic}
            程度：{level}
            
            請給出簡短的學習建議（1-2句話），包括：
            1. 鼓勵的話
            2. 一個具體的改進建議
            
            請用友善鼓勵的語氣。
            """
            
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "你是友善的英語老師，給出簡短有用的建議。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=200,
                temperature=0.5
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"建議生成失敗: {e}")
            return "很好的嘗試！繼續練習會讓您的英語更流利。"
    
    def _get_fallback_feedback(self, text: str) -> str:
        """備用回饋訊息"""
        return f"""
🎤 **語音轉文字結果：**
"{text}"

📝 **回饋：**
很棒的語音練習！您的發音很清楚。

✨ **建議：**
• 繼續保持練習的習慣
• 嘗試使用更多詞彙
• 表達更完整的想法

💪 繼續加油，您做得很好！
        """.strip()

# 整合到現有系統的簡化處理函數
def process_voice_simple(openai_client, transcribed_text: str, topic: str, user_level: str) -> str:
    """簡化的語音處理函數"""
    try:
        feedback_processor = SimpleVoiceFeedback(openai_client)
        return feedback_processor.generate_simple_feedback(transcribed_text, topic, user_level)
    except Exception as e:
        logger.error(f"簡化語音處理失敗: {e}")
        return f"""
🎤 **語音轉文字結果：**
"{transcribed_text}"

📝 **狀態：**
語音識別成功！回饋系統正在優化中。

💪 **鼓勵：**
很棒的練習！請繼續保持學習的熱忱。

🎯 **建議：**
• 多練習口說
• 嘗試不同主題
• 保持學習動力

繼續加油！ 😊
        """.strip()
