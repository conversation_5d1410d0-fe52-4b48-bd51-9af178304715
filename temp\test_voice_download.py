#!/usr/bin/env python3
"""
測試語音下載功能
除錯語音處理的各個步驟
"""

import os
import requests
import tempfile
from dotenv import load_dotenv
from openai import OpenAI

# 載入環境變數
load_dotenv()

def test_line_voice_download():
    """測試 LINE 語音下載功能"""
    
    print("🧪 測試 LINE 語音下載功能")
    print("=" * 50)
    
    # 模擬 LINE 語音下載
    def download_voice_message_test(message_id: str, audio_url: str):
        """測試語音下載函數"""
        try:
            print(f"嘗試下載語音: {audio_url}")
            
            # LINE Bot API 需要特殊的 headers
            headers = {
                'Authorization': f'Bearer {os.getenv("LINE_CHANNEL_ACCESS_TOKEN")}'
            }
            
            print(f"使用 Token: {os.getenv('LINE_CHANNEL_ACCESS_TOKEN')[:20]}...")
            
            response = requests.get(audio_url, headers=headers, timeout=10)
            print(f"HTTP 狀態碼: {response.status_code}")
            print(f"回應 headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                # 保存到臨時檔案
                with tempfile.NamedTemporaryFile(delete=False, suffix='.m4a') as temp_file:
                    temp_file.write(response.content)
                    print(f"✅ 語音檔案下載成功: {temp_file.name}")
                    print(f"檔案大小: {len(response.content)} bytes")
                    return temp_file.name
            else:
                print(f"❌ 下載失敗: {response.status_code}")
                print(f"錯誤內容: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 下載異常: {e}")
            return None
    
    # 測試下載函數
    test_message_id = "test_123"
    test_audio_url = "https://api-data.line.me/v2/bot/message/test_123/content"
    
    result = download_voice_message_test(test_message_id, test_audio_url)
    
    if result:
        print(f"下載結果: {result}")
        
        # 測試 Whisper API
        test_whisper_api(result)
    else:
        print("語音下載失敗，無法進行後續測試")

def test_whisper_api(audio_file_path: str):
    """測試 Whisper API"""
    
    print(f"\n🧪 測試 Whisper API")
    print("=" * 50)
    
    try:
        openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        
        print(f"測試檔案: {audio_file_path}")
        print(f"檔案存在: {os.path.exists(audio_file_path)}")
        
        if os.path.exists(audio_file_path):
            file_size = os.path.getsize(audio_file_path)
            print(f"檔案大小: {file_size} bytes")
            
            if file_size > 0:
                with open(audio_file_path, 'rb') as audio_file:
                    print("呼叫 Whisper API...")
                    
                    transcript = openai_client.audio.transcriptions.create(
                        model="whisper-1",
                        file=audio_file,
                        language="en"
                    )
                    
                    print(f"✅ 轉錄成功: {transcript.text}")
                    return transcript.text
            else:
                print("❌ 檔案為空")
        else:
            print("❌ 檔案不存在")
            
    except Exception as e:
        print(f"❌ Whisper API 失敗: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理檔案
        try:
            if os.path.exists(audio_file_path):
                os.unlink(audio_file_path)
                print(f"已清理檔案: {audio_file_path}")
        except:
            pass

def create_mock_voice_handler():
    """創建模擬語音處理器"""
    
    print("\n🔧 創建模擬語音處理器")
    print("=" * 50)
    
    mock_handler = '''
def handle_audio_message_mock(event):
    """模擬語音處理器 - 用於除錯"""
    user_id = event.source.user_id
    message_id = event.message.id
    duration = event.message.duration
    
    logger.info(f"🎤 收到語音訊息: {message_id}")
    
    # 模擬處理步驟
    steps = [
        "✅ 語音訊息接收成功",
        "🔄 正在下載語音檔案...",
        "🔄 正在進行語音轉文字...",
        "🔄 正在生成 AI 回應...",
        "🔄 正在生成語音回覆...",
        "✅ 處理完成！"
    ]
    
    response = f"""
🎤 **語音處理除錯模式**

訊息ID: {message_id}
時長: {duration}ms
用戶: {user_id}

📋 **處理步驟：**
""" + "\\n".join(f"{i+1}. {step}" for i, step in enumerate(steps)) + """

🔧 **除錯資訊：**
• LINE Token 已設定
• OpenAI API 已設定
• gTTS 模組已載入
• 靜態檔案服務已啟動

💡 **下一步：**
檢查語音下載和 Whisper API 調用
    """
    
    return response.strip()
    '''
    
    print("✅ 模擬語音處理器已準備")
    return mock_handler

def check_environment():
    """檢查環境設定"""
    
    print("🔧 檢查環境設定")
    print("=" * 50)
    
    # 檢查必要的環境變數
    required_vars = [
        "LINE_CHANNEL_ACCESS_TOKEN",
        "LINE_CHANNEL_SECRET", 
        "OPENAI_API_KEY"
    ]
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value[:20]}...")
        else:
            print(f"❌ {var}: 未設定")
    
    # 檢查模組
    try:
        from voice_conversation_module import VoiceConversationModule
        print("✅ voice_conversation_module 可導入")
    except Exception as e:
        print(f"❌ voice_conversation_module 導入失敗: {e}")
    
    try:
        from gtts import gTTS
        print("✅ gTTS 可導入")
    except Exception as e:
        print(f"❌ gTTS 導入失敗: {e}")
    
    try:
        openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        print("✅ OpenAI 客戶端可初始化")
    except Exception as e:
        print(f"❌ OpenAI 客戶端初始化失敗: {e}")

if __name__ == "__main__":
    print("🎤 語音功能除錯工具")
    print("=" * 60)
    
    check_environment()
    create_mock_voice_handler()
    
    print("\n📋 除錯步驟建議：")
    print("1. 檢查環境變數設定")
    print("2. 測試語音下載功能")
    print("3. 測試 Whisper API")
    print("4. 測試 gTTS 語音生成")
    print("5. 測試完整流程")
    
    print("\n💡 常見問題：")
    print("• LINE Token 權限不足")
    print("• 語音檔案格式不支援")
    print("• OpenAI API 配額不足")
    print("• 網路連接問題")
    
    # 注意：實際的語音下載測試需要真實的 message_id
    print("\n⚠️  注意：語音下載測試需要真實的 LINE 語音訊息 ID")
