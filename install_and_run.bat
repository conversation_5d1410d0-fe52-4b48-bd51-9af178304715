@echo off
echo ========================================
echo AutoGen Line Bot 安裝和啟動腳本
echo ========================================

echo.
echo 1. 檢查 Python 版本...
python --version
if %errorlevel% neq 0 (
    echo 錯誤: Python 未安裝或未加入 PATH
    pause
    exit /b 1
)

echo.
echo 2. 升級 pip...
python -m pip install --upgrade pip

echo.
echo 3. 安裝基本依賴...
pip install fastapi==0.104.1
pip install uvicorn[standard]==0.24.0
pip install line-bot-sdk==3.7.0
pip install openai
pip install requests
pip install pydantic==2.5.0

echo.
echo 4. 檢查環境變數...
if "%LINE_CHANNEL_ACCESS_TOKEN%"=="" (
    echo 警告: LINE_CHANNEL_ACCESS_TOKEN 未設定
    echo 請設定環境變數或編輯 .env 文件
)

if "%LINE_CHANNEL_SECRET%"=="" (
    echo 警告: LINE_CHANNEL_SECRET 未設定
    echo 請設定環境變數或編輯 .env 文件
)

if "%OPENAI_API_KEY%"=="" (
    echo 警告: OPENAI_API_KEY 未設定
    echo 請設定環境變數或編輯 .env 文件
)

echo.
echo 5. 啟動 Line Bot...
python run_simple_line_bot.py

pause
