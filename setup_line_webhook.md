# Line Bot Webhook 設定指南

## 🚀 快速設定步驟

### 1. 啟動修正版 Line Bot
```bash
python line_bot_fixed.py
```

### 2. 設定 ngrok (新的終端視窗)
```bash
ngrok http 5000
```

### 3. 複製 ngrok URL
ngrok 會顯示類似這樣的 URL：
```
https://abc123.ngrok.io -> http://localhost:5000
```

### 4. 設定 Line Developers Console

1. 前往 [Line Developers Console](https://developers.line.biz/)
2. 選擇您的 Bot
3. 前往 **Messaging API** 頁籤
4. 在 **Webhook settings** 中：
   - **Webhook URL**: `https://your-ngrok-url.ngrok.io/webhook`
   - 點擊 **Verify** 按鈕
   - 應該會看到 ✅ 成功訊息

### 5. 啟用 Webhook
- 確保 **Use webhook** 開關是開啟的
- 確保 **Auto-reply messages** 是關閉的

## 🔍 測試步驟

### 1. 測試基本端點
在瀏覽器中訪問：
- `https://your-ngrok-url.ngrok.io/` - 應該看到 Bot 狀態
- `https://your-ngrok-url.ngrok.io/health` - 應該看到健康檢查

### 2. 測試 Line Bot
1. 掃描 Line Developers Console 中的 QR Code
2. 加入您的 Bot 為好友
3. 發送訊息測試：
   - `/start` - 開始對話
   - `/help` - 顯示幫助
   - `Hello` - 一般英語對話

## 🐛 故障排除

### Webhook 驗證失敗 (400 錯誤)
- 檢查 ngrok URL 是否正確
- 確保 URL 包含 `/webhook` 路徑
- 檢查 Bot 是否在 port 5000 運行

### 簽名驗證失敗
- 檢查 `LINE_CHANNEL_SECRET` 環境變數
- 確保沒有多餘的空格或換行

### AI 回應失敗
- 檢查 `OPENAI_API_KEY` 環境變數
- 確保 OpenAI API 額度充足

## 📋 檢查清單

- [ ] Bot 在 port 5000 運行
- [ ] ngrok 轉發 port 5000
- [ ] Webhook URL 設定正確 (包含 /webhook)
- [ ] 環境變數設定完整
- [ ] Line Bot 加為好友
- [ ] 測試訊息發送成功

## 🎯 成功指標

當一切設定正確時，您應該看到：

1. **Line Developers Console**: Webhook 驗證成功 ✅
2. **Bot 日誌**: 收到和發送訊息的日誌
3. **Line App**: Bot 回覆您的訊息

## 📞 支援

如果遇到問題：
1. 檢查 Bot 日誌輸出
2. 檢查 ngrok 日誌
3. 確認環境變數設定
4. 測試基本端點是否可訪問
