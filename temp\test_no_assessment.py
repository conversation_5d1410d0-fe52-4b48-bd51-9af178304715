#!/usr/bin/env python3
"""
測試無分級測驗的語音功能
"""

import os
from dotenv import load_dotenv
from topic_management_module import TopicManagementModule
from voice_feedback_simple import process_voice_simple
from openai import OpenAI

# 載入環境變數
load_dotenv()

def test_topic_without_assessment():
    """測試無需分級測驗的主題功能"""
    
    print("🧪 測試無分級測驗的主題功能")
    print("=" * 50)
    
    # 初始化主題管理
    topic_management = TopicManagementModule()
    
    # 測試獲取主題（使用預設中級程度）
    user_id = "test_user_123"
    default_level = "intermediate"
    
    try:
        daily_topic = topic_management.get_daily_topic(user_id, default_level)
        topic_message = topic_management.format_topic_message(daily_topic)
        
        print("✅ 主題獲取成功:")
        print(f"主題ID: {daily_topic.id}")
        print(f"主題名稱: {daily_topic.name}")
        print(f"難度: {daily_topic.difficulty.value}")
        print("-" * 30)
        print("格式化訊息:")
        print(topic_message)
        
    except Exception as e:
        print(f"❌ 主題獲取失敗: {e}")
    
    print("\n" + "=" * 50)

def test_voice_without_assessment():
    """測試無需分級測驗的語音回饋"""
    
    print("🧪 測試無分級測驗的語音回饋")
    print("=" * 50)
    
    # 初始化 OpenAI 客戶端
    openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    
    # 測試語音回饋
    test_text = "I want to practice English conversation"
    test_topic = "職場生活"
    default_level = "intermediate"
    
    try:
        feedback = process_voice_simple(openai_client, test_text, test_topic, default_level)
        
        print("✅ 語音回饋成功:")
        print(feedback)
        
    except Exception as e:
        print(f"❌ 語音回饋失敗: {e}")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    test_topic_without_assessment()
    test_voice_without_assessment()
    
    print("\n🎉 測試完成！")
    print("現在您可以直接使用語音功能，無需分級測驗：")
    print("• 發送 /start - 查看歡迎訊息")
    print("• 發送 /topic - 獲取今日主題")
    print("• 錄製語音訊息 - 直接獲得回饋")
    print("• 發送 /help - 查看所有功能")
