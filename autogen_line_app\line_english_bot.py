"""
Line 英語口說學習 Bot - 最終實現版本
整合 AutoGen 多代理系統，提供完整的英語學習體驗
"""

# 標準庫導入
import asyncio
import json
import logging
import os
import tempfile
import uuid
from dataclasses import dataclass, asdict
from datetime import datetime
from typing import Any, Dict, List, Optional

# 第三方庫導入
try:
    import speech_recognition as sr
except ImportError:
    print("警告: speech_recognition 未安裝，語音功能將不可用")
    sr = None

try:
    from fastapi import FastAPI, HTTPException, Request
    from fastapi.responses import JSONResponse
except ImportError:
    print("錯誤: FastAPI 未安裝，請執行: pip install fastapi uvicorn")
    raise

try:
    from pydub import AudioSegment
except ImportError:
    print("警告: pydub 未安裝，音頻處理功能將不可用")
    AudioSegment = None

# AutoGen 導入
try:
    from autogen_agentchat.agents import AssistantAgent
    from autogen_agentchat.teams import RoundRobinGroupChat
    from autogen_ext.models.openai import OpenAIChatCompletionClient
except ImportError:
    print("錯誤: AutoGen 套件未安裝，請執行: pip install autogen-agentchat autogen-ext[openai]")
    raise

# Line Bot 導入
try:
    from linebot.v3 import WebhookHandler
    from linebot.v3.exceptions import InvalidSignatureError
    from linebot.v3.messaging import (
        ApiClient, AudioMessage, Configuration, MessagingApi,
        ReplyMessageRequest, TextMessage
    )
    from linebot.v3.webhooks import (
        AudioMessageContent, MessageEvent, TextMessageContent
    )
except ImportError:
    print("錯誤: Line Bot SDK 未安裝，請執行: pip install line-bot-sdk")
    raise

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class Config:
    """應用程式配置管理"""
    
    def __init__(self):
        self.line_channel_access_token = self._get_env_var('LINE_CHANNEL_ACCESS_TOKEN')
        self.line_channel_secret = self._get_env_var('LINE_CHANNEL_SECRET')
        self.openai_api_key = self._get_env_var('OPENAI_API_KEY')
        self.azure_speech_key = os.getenv('AZURE_SPEECH_KEY')
        self.azure_speech_region = os.getenv('AZURE_SPEECH_REGION', 'eastus')
        
        # 驗證必要配置
        self._validate_config()
    
    def _get_env_var(self, var_name: str) -> str:
        """安全獲取環境變數"""
        value = os.getenv(var_name)
        if not value:
            raise ValueError(f"環境變數 {var_name} 未設定")
        return value
    
    def _validate_config(self) -> None:
        """驗證配置完整性"""
        required_vars = [
            'line_channel_access_token',
            'line_channel_secret', 
            'openai_api_key'
        ]
        
        for var in required_vars:
            if not getattr(self, var):
                raise ValueError(f"必要配置 {var} 缺失")


@dataclass
class StudentProfile:
    """學員檔案資料類"""
    user_id: str
    name: str = ""
    english_level: str = "beginner"
    vocabulary_count: int = 0
    conversation_count: int = 0
    last_topic: str = ""
    learning_goals: List[str] = None
    created_at: datetime = None
    updated_at: datetime = None
    
    def __post_init__(self):
        if self.learning_goals is None:
            self.learning_goals = []
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return asdict(self)
    
    def update_progress(self, vocabulary_learned: int = 0, conversation_completed: bool = False):
        """更新學習進度"""
        self.vocabulary_count += vocabulary_learned
        if conversation_completed:
            self.conversation_count += 1
        self.updated_at = datetime.now()


@dataclass
class ConversationSession:
    """對話會話資料類"""
    session_id: str
    user_id: str
    topic: str
    messages: List[Dict[str, Any]] = None
    vocabulary_learned: List[str] = None
    feedback: Dict[str, Any] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.messages is None:
            self.messages = []
        if self.vocabulary_learned is None:
            self.vocabulary_learned = []
        if self.feedback is None:
            self.feedback = {}
        if self.created_at is None:
            self.created_at = datetime.now()
    
    def add_message(self, role: str, content: str, metadata: Dict[str, Any] = None):
        """添加訊息到會話"""
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "metadata": metadata or {}
        }
        self.messages.append(message)


class SpeechProcessor:
    """語音處理器"""
    
    def __init__(self, config: Config):
        self.config = config
        self.recognizer = sr.Recognizer()
        
        # 優化識別器設定
        self.recognizer.energy_threshold = 300
        self.recognizer.dynamic_energy_threshold = True
        self.recognizer.pause_threshold = 0.8
        
    async def audio_to_text(self, audio_data: bytes, language: str = 'en-US') -> str:
        """將音頻轉換為文字"""
        temp_file_path = None
        try:
            # 創建臨時文件
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name
            
            # 語音識別
            with sr.AudioFile(temp_file_path) as source:
                # 調整環境噪音
                self.recognizer.adjust_for_ambient_noise(source, duration=0.5)
                audio = self.recognizer.record(source)
                
                # 使用 Google Speech Recognition
                text = self.recognizer.recognize_google(
                    audio, 
                    language=language,
                    show_all=False
                )
                
                logger.info(f"語音識別成功: {text[:50]}...")
                return text
                
        except sr.UnknownValueError:
            logger.warning("語音識別無法理解音頻內容")
            return ""
        except sr.RequestError as e:
            logger.error(f"語音識別服務錯誤: {e}")
            return ""
        except Exception as e:
            logger.error(f"語音處理意外錯誤: {e}")
            return ""
        finally:
            # 清理臨時文件
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                except OSError as e:
                    logger.warning(f"清理臨時文件失敗: {e}")


class EnglishTeacherAgent:
    """英語老師代理"""
    
    def __init__(self, model_client: OpenAIChatCompletionClient):
        self.model_client = model_client
        self.agent = AssistantAgent(
            name="english_teacher",
            model_client=model_client,
            system_message=self._get_system_message(),
            tools=[self.provide_vocabulary_help, self.give_pronunciation_feedback],
            reflect_on_tool_use=True
        )
    
    def _get_system_message(self) -> str:
        """獲取系統訊息"""
        return """
        你是一位專業且友善的英語口說老師 Emma。你的職責包括：
        
        1. 🗣️ 與學員進行自然的英語對話練習
        2. ✏️ 溫和地糾正語法和發音錯誤
        3. 📚 提供實用的詞彙建議和同義詞
        4. 💪 給予積極鼓勵和建設性回饋
        5. 🎯 根據學員程度調整對話難度
        6. 🌟 創造輕鬆愉快的學習氛圍
        
        回應原則：
        - 主要用英語回應，必要時提供中文解釋
        - 保持耐心、鼓勵和支持的態度
        - 提供具體、可行的改進建議
        - 適時讚美學員的進步
        """
    
    async def provide_vocabulary_help(self, word: str) -> str:
        """提供詞彙幫助"""
        try:
            help_text = f"""
            📖 Word: {word}
            🔍 Definition: [AI will provide definition]
            🔄 Synonyms: [AI will provide synonyms]
            📝 Example: [AI will provide example sentence]
            💡 Usage tip: [AI will provide usage advice]
            """
            return help_text
        except Exception as e:
            logger.error(f"詞彙幫助錯誤: {e}")
            return f"Sorry, I couldn't provide help for the word '{word}' right now."
    
    async def give_pronunciation_feedback(self, text: str) -> str:
        """提供發音回饋"""
        try:
            feedback = f"""
            🎤 Pronunciation Analysis for: "{text}"
            
            ✅ Good points: [AI will analyze good aspects]
            🎯 Areas to improve: [AI will suggest improvements]
            🔊 Practice tip: [AI will provide practice advice]
            """
            return feedback
        except Exception as e:
            logger.error(f"發音回饋錯誤: {e}")
            return "I'll help you with pronunciation in our next conversation!"


class ConversationManagerAgent:
    """對話管理代理"""
    
    def __init__(self, model_client: OpenAIChatCompletionClient):
        self.model_client = model_client
        self.agent = AssistantAgent(
            name="conversation_manager",
            model_client=model_client,
            system_message="""
            你是對話管理專家 Alex，負責：
            1. 📋 管理對話流程和主題轉換
            2. 📊 追蹤學員學習進度和成果
            3. 🎯 推薦適合的練習內容和難度
            4. 💾 記錄和分析學習數據
            5. 🔄 協調多代理間的協作
            """,
            tools=[self.track_progress, self.recommend_topics],
            reflect_on_tool_use=True
        )
        
        # 主題庫
        self.topic_library = {
            "beginner": ["自我介紹與問候", "家庭與朋友", "日常活動", "食物與飲料"],
            "intermediate": ["工作與職業", "教育經歷", "文化差異", "健康與運動"],
            "advanced": ["商務談判", "學術討論", "社會議題", "哲學思考"]
        }
    
    async def track_progress(self, user_id: str, session_data: Dict[str, Any]) -> str:
        """追蹤學習進度"""
        try:
            progress_info = f"""
            📈 Progress Update for User {user_id}:
            - Session completed: {session_data.get('completed', False)}
            - New vocabulary: {len(session_data.get('vocabulary', []))}
            - Conversation quality: {session_data.get('quality_score', 'N/A')}
            """
            logger.info(f"進度追蹤: {user_id}")
            return progress_info
        except Exception as e:
            logger.error(f"進度追蹤錯誤: {e}")
            return "Progress tracking temporarily unavailable."
    
    async def recommend_topics(self, user_level: str, interests: List[str] = None) -> List[str]:
        """推薦對話主題"""
        try:
            base_topics = self.topic_library.get(user_level, self.topic_library["beginner"])
            return base_topics[:5]
        except Exception as e:
            logger.error(f"主題推薦錯誤: {e}")
            return ["自我介紹"]


class AssessmentAgent:
    """評估代理"""
    
    def __init__(self, model_client: OpenAIChatCompletionClient):
        self.model_client = model_client
        self.agent = AssistantAgent(
            name="assessment_agent",
            model_client=model_client,
            system_message="""
            你是英語能力評估專家 Dr. Smith，專精於：
            1. 🎯 準確評估學員英語程度
            2. 🔍 深入分析語法和詞彙使用
            3. 📋 提供詳細的學習建議
            4. 📊 生成個人化學習報告
            """,
            tools=[self.assess_level, self.analyze_grammar],
            reflect_on_tool_use=True
        )
    
    async def assess_level(self, conversation_text: str) -> str:
        """評估英語程度"""
        try:
            word_count = len(conversation_text.split())
            complex_words = sum(1 for word in conversation_text.split() if len(word) > 6)
            complexity_ratio = complex_words / word_count if word_count > 0 else 0
            
            if complexity_ratio > 0.3:
                level = "advanced"
            elif complexity_ratio > 0.15:
                level = "intermediate"
            else:
                level = "beginner"
            
            logger.info(f"程度評估完成: {level}")
            return level
            
        except Exception as e:
            logger.error(f"程度評估錯誤: {e}")
            return "beginner"
    
    async def analyze_grammar(self, text: str) -> Dict[str, Any]:
        """分析語法"""
        try:
            analysis = {
                "errors": [],
                "suggestions": [],
                "score": 85,
                "areas_to_improve": ["時態一致性", "介詞使用"]
            }
            return analysis
        except Exception as e:
            logger.error(f"語法分析錯誤: {e}")
            return {"errors": [], "suggestions": [], "score": 0}


class DataManager:
    """資料管理器"""

    def __init__(self):
        self.student_profiles: Dict[str, StudentProfile] = {}
        self.conversation_sessions: Dict[str, ConversationSession] = {}

    async def get_or_create_student_profile(self, user_id: str) -> StudentProfile:
        """獲取或建立學員檔案"""
        if user_id not in self.student_profiles:
            self.student_profiles[user_id] = StudentProfile(user_id=user_id)
            logger.info(f"建立新學員檔案: {user_id}")
        return self.student_profiles[user_id]

    async def save_conversation_session(self, session: ConversationSession) -> bool:
        """保存對話會話"""
        try:
            self.conversation_sessions[session.session_id] = session
            logger.info(f"會話已保存: {session.session_id}")
            return True
        except Exception as e:
            logger.error(f"保存會話失敗: {e}")
            return False

    async def get_user_sessions(self, user_id: str, limit: int = 10) -> List[ConversationSession]:
        """獲取用戶的對話會話"""
        sessions = [
            session for session in self.conversation_sessions.values()
            if session.user_id == user_id
        ]
        return sorted(sessions, key=lambda x: x.created_at, reverse=True)[:limit]


class LineEnglishBot:
    """Line 英語學習 Bot 主類"""

    def __init__(self):
        # 初始化配置
        self.config = Config()

        # 初始化 Line Bot
        self.line_config = Configuration(access_token=self.config.line_channel_access_token)
        self.handler = WebhookHandler(self.config.line_channel_secret)

        # 初始化 OpenAI 客戶端
        self.model_client = OpenAIChatCompletionClient(
            model="gpt-4o",
            api_key=self.config.openai_api_key
        )

        # 初始化組件
        self.speech_processor = SpeechProcessor(self.config)
        self.data_manager = DataManager()

        # 初始化代理
        self.teacher_agent = EnglishTeacherAgent(self.model_client)
        self.conversation_manager = ConversationManagerAgent(self.model_client)
        self.assessment_agent = AssessmentAgent(self.model_client)

        # 初始化多代理團隊
        self.agent_team = RoundRobinGroupChat([
            self.teacher_agent.agent,
            self.conversation_manager.agent,
            self.assessment_agent.agent
        ])

        # 初始化 FastAPI 應用
        self.app = FastAPI(
            title="Line English Learning Bot",
            description="AI-powered English conversation practice bot",
            version="2.0.0"
        )

        self._setup_routes()
        self._setup_handlers()

    def _setup_routes(self) -> None:
        """設定 API 路由"""

        @self.app.get("/")
        async def root():
            return {
                "message": "Line English Learning Bot is running!",
                "version": "2.0.0",
                "status": "healthy"
            }

        @self.app.get("/health")
        async def health_check():
            return {"status": "healthy", "timestamp": datetime.now().isoformat()}

        @self.app.post("/webhook")
        async def webhook(request: Request):
            """Line Webhook 處理"""
            signature = request.headers.get('X-Line-Signature', '')
            body = await request.body()

            try:
                self.handler.handle(body.decode('utf-8'), signature)
                return JSONResponse(content={"status": "ok"})
            except InvalidSignatureError:
                logger.error("無效的 Line 簽名")
                raise HTTPException(status_code=400, detail="Invalid signature")
            except Exception as e:
                logger.error(f"Webhook 處理錯誤: {e}")
                raise HTTPException(status_code=500, detail="Internal server error")

        @self.app.get("/api/student/{user_id}/profile")
        async def get_student_profile(user_id: str):
            """獲取學員檔案"""
            try:
                profile = await self.data_manager.get_or_create_student_profile(user_id)
                return profile.to_dict()
            except Exception as e:
                logger.error(f"獲取學員檔案錯誤: {e}")
                raise HTTPException(status_code=500, detail="Failed to get profile")

    def _setup_handlers(self) -> None:
        """設定 Line 事件處理器"""

        @self.handler.add(MessageEvent, message=TextMessageContent)
        async def handle_text_message(event):
            """處理文字訊息"""
            try:
                await self._process_text_message(event)
            except Exception as e:
                logger.error(f"處理文字訊息錯誤: {e}")
                await self._send_error_message(event.reply_token)

        @self.handler.add(MessageEvent, message=AudioMessageContent)
        async def handle_audio_message(event):
            """處理語音訊息"""
            try:
                await self._process_audio_message(event)
            except Exception as e:
                logger.error(f"處理語音訊息錯誤: {e}")
                await self._send_error_message(event.reply_token)

    async def _process_text_message(self, event) -> None:
        """處理文字訊息"""
        user_id = event.source.user_id
        user_message = event.message.text.strip()

        # 獲取學員檔案
        profile = await self.data_manager.get_or_create_student_profile(user_id)

        # 特殊命令處理
        if user_message.lower() in ['/start', '開始', 'start']:
            response = await self._handle_start_command(profile)
        elif user_message.lower() in ['/help', '幫助', 'help']:
            response = await self._handle_help_command()
        elif user_message.lower().startswith('/level'):
            response = await self._handle_level_assessment(profile, user_message)
        else:
            # 一般對話處理
            response = await self._handle_conversation(profile, user_message)

        # 發送回覆
        await self._send_text_reply(event.reply_token, response)

    async def _process_audio_message(self, event) -> None:
        """處理語音訊息"""
        user_id = event.source.user_id

        try:
            # 下載音頻
            with ApiClient(self.line_config) as api_client:
                line_bot_api = MessagingApi(api_client)
                audio_content = line_bot_api.get_message_content(event.message.id)

            # 語音轉文字
            text = await self.speech_processor.audio_to_text(audio_content)

            if text:
                # 獲取學員檔案
                profile = await self.data_manager.get_or_create_student_profile(user_id)

                # 處理語音內容
                response = await self._handle_voice_conversation(profile, text)

                # 發送回覆
                await self._send_text_reply(event.reply_token, response)
            else:
                await self._send_text_reply(
                    event.reply_token,
                    "Sorry, I couldn't understand your voice message. Please try again! 😊"
                )

        except Exception as e:
            logger.error(f"語音處理錯誤: {e}")
            await self._send_error_message(event.reply_token)

    async def _handle_start_command(self, profile: StudentProfile) -> str:
        """處理開始命令"""
        if profile.english_level == "beginner" and profile.conversation_count == 0:
            return """
🌟 Welcome to English Learning Bot!

I'm Emma, your AI English teacher. Let's start your English journey!

First, let's assess your English level. Please answer this question:
"Tell me about yourself - your name, where you're from, and what you like to do."

You can send text or voice messages. I'm here to help! 😊
            """.strip()
        else:
            return f"""
👋 Welcome back!

Your current level: {profile.english_level.title()}
Conversations completed: {profile.conversation_count}
Vocabulary learned: {profile.vocabulary_count} words

What would you like to practice today?
1. Free conversation
2. Topic-based practice
3. Vocabulary review
4. Pronunciation practice

Just tell me what interests you! 🎯
            """.strip()

    async def _handle_help_command(self) -> str:
        """處理幫助命令"""
        return """
📚 How to use English Learning Bot:

🗣️ **Voice Practice**: Send voice messages for pronunciation feedback
💬 **Text Chat**: Type messages for grammar and vocabulary help
🎯 **Commands**:
   • /start - Begin or restart your learning journey
   • /level - Take a level assessment
   • /help - Show this help message

🌟 **Features**:
   • Personalized conversation practice
   • Real-time grammar correction
   • Vocabulary building
   • Pronunciation feedback
   • Progress tracking

Ready to practice? Just start talking! 😊
        """.strip()

    async def _handle_level_assessment(self, profile: StudentProfile, message: str) -> str:
        """處理程度評估"""
        assessment_text = message.replace('/level', '').strip()

        if not assessment_text:
            return """
📊 Let's assess your English level!

Please answer these questions:
1. Introduce yourself
2. Describe your daily routine
3. What are your goals for learning English?

Send your answers and I'll evaluate your level! 🎯
            """.strip()

        # 使用評估代理分析
        level = await self.assessment_agent.assess_level(assessment_text)
        profile.english_level = level
        profile.updated_at = datetime.now()

        return f"""
✅ Assessment Complete!

Your English level: **{level.title()}**

Based on your level, I recommend:
{await self._get_level_recommendations(level)}

Let's start practicing! What topic interests you? 🚀
        """.strip()

    async def _handle_conversation(self, profile: StudentProfile, message: str) -> str:
        """處理一般對話"""
        try:
            # 使用多代理系統處理對話
            context = f"""
            Student Profile:
            - Level: {profile.english_level}
            - Conversations: {profile.conversation_count}
            - Vocabulary: {profile.vocabulary_count}

            Student Message: {message}

            Please provide English learning guidance and engaging conversation.
            """

            response = await self.agent_team.run(task=context)

            # 更新學員進度
            profile.conversation_count += 1
            profile.updated_at = datetime.now()

            return str(response)

        except Exception as e:
            logger.error(f"對話處理錯誤: {e}")
            return "I'm having some technical difficulties. Let's try again! 😊"

    async def _handle_voice_conversation(self, profile: StudentProfile, text: str) -> str:
        """處理語音對話"""
        # 語音對話包含額外的發音分析
        conversation_response = await self._handle_conversation(profile, text)

        # 添加發音回饋
        pronunciation_feedback = await self.teacher_agent.give_pronunciation_feedback(text)

        return f"{conversation_response}\\n\\n{pronunciation_feedback}"

    async def _get_level_recommendations(self, level: str) -> str:
        """獲取程度建議"""
        recommendations = {
            "beginner": "• Daily conversation practice\\n• Basic vocabulary building\\n• Simple grammar exercises",
            "intermediate": "• Topic-based discussions\\n• Advanced vocabulary\\n• Complex sentence structures",
            "advanced": "• Business English\\n• Academic discussions\\n• Idiomatic expressions"
        }
        return recommendations.get(level, recommendations["beginner"])

    async def _send_text_reply(self, reply_token: str, text: str) -> None:
        """發送文字回覆"""
        try:
            with ApiClient(self.line_config) as api_client:
                line_bot_api = MessagingApi(api_client)
                line_bot_api.reply_message(
                    ReplyMessageRequest(
                        reply_token=reply_token,
                        messages=[TextMessage(text=text)]
                    )
                )
        except Exception as e:
            logger.error(f"發送回覆錯誤: {e}")

    async def _send_error_message(self, reply_token: str) -> None:
        """發送錯誤訊息"""
        error_message = "Sorry, I encountered an error. Please try again later. 😔"
        await self._send_text_reply(reply_token, error_message)

    async def run(self, host: str = "0.0.0.0", port: int = 8000) -> None:
        """啟動 Bot"""
        import uvicorn

        logger.info(f"🚀 啟動 Line English Learning Bot")
        logger.info(f"📍 服務地址: http://{host}:{port}")

        uvicorn.run(self.app, host=host, port=port, log_level="info")


# 主程式入口
async def main():
    """主程式"""
    try:
        bot = LineEnglishBot()
        await bot.run()
    except Exception as e:
        logger.error(f"應用程式啟動失敗: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
