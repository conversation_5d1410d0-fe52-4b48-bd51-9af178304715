#!/usr/bin/env python3
"""
分級測驗整合模組
將分級系統整合到 LINE Bot 中

功能：
1. 分級測驗流程管理
2. 用戶狀態追蹤
3. 測驗結果存儲
4. 學習路徑推薦
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

from level_assessment_module import LevelAssessmentModule, EnglishLevel, AssessmentResult

# 設定日誌
logger = logging.getLogger(__name__)

class AssessmentState(Enum):
    """測驗狀態"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"

@dataclass
class UserAssessmentSession:
    """用戶測驗會話"""
    user_id: str
    state: AssessmentState
    current_question_index: int
    questions: List[Dict]
    responses: List[Dict]
    started_at: datetime
    completed_at: Optional[datetime] = None

class AssessmentIntegration:
    """分級測驗整合管理器"""
    
    def __init__(self, openai_client):
        """初始化整合管理器"""
        self.openai_client = openai_client
        self.assessment_module = LevelAssessmentModule(openai_client)
        self.active_sessions: Dict[str, UserAssessmentSession] = {}
        self.user_profiles: Dict[str, Dict] = {}
    
    def start_assessment(self, user_id: str) -> str:
        """開始分級測驗"""
        try:
            # 檢查是否已有進行中的測驗
            if user_id in self.active_sessions:
                session = self.active_sessions[user_id]
                if session.state == AssessmentState.IN_PROGRESS:
                    return self._get_current_question_message(session)
            
            # 獲取測驗問題
            questions = self.assessment_module.get_assessment_questions(3)
            
            # 創建新的測驗會話
            session = UserAssessmentSession(
                user_id=user_id,
                state=AssessmentState.IN_PROGRESS,
                current_question_index=0,
                questions=[asdict(q) for q in questions],
                responses=[],
                started_at=datetime.now()
            )
            
            self.active_sessions[user_id] = session
            
            # 返回歡迎訊息和第一個問題
            welcome_message = """
🎯 **英語程度分級測驗**

歡迎來到英語學習分級測驗！我將通過 3 個問題來了解您的英語程度，以便為您推薦最適合的學習內容。

📝 **測驗說明：**
• 請用英語回答每個問題
• 盡量表達完整的想法
• 不用擔心犯錯，這是學習的一部分！

讓我們開始吧！ 😊

---
            """
            
            first_question = self._get_current_question_message(session)
            return welcome_message + first_question
            
        except Exception as e:
            logger.error(f"開始分級測驗時發生錯誤: {e}")
            return "抱歉，分級測驗系統暫時無法使用。請稍後再試，或直接開始學習對話。"
    
    def process_assessment_response(self, user_id: str, response: str) -> str:
        """處理測驗回答"""
        try:
            # 檢查是否有進行中的測驗
            if user_id not in self.active_sessions:
                return "您目前沒有進行中的分級測驗。請發送 /start 開始測驗。"
            
            session = self.active_sessions[user_id]
            
            if session.state != AssessmentState.IN_PROGRESS:
                return "您的分級測驗已經完成。如需重新測驗，請發送 /assessment。"
            
            # 記錄當前回答
            current_question = session.questions[session.current_question_index]
            session.responses.append({
                "question_id": current_question["id"],
                "question": current_question["question"],
                "response": response,
                "timestamp": datetime.now().isoformat()
            })
            
            # 移動到下一個問題
            session.current_question_index += 1
            
            # 檢查是否完成所有問題
            if session.current_question_index >= len(session.questions):
                return self._complete_assessment(session)
            else:
                # 返回下一個問題
                progress = f"✅ 問題 {session.current_question_index}/{len(session.questions)} 完成\n\n"
                next_question = self._get_current_question_message(session)
                return progress + next_question
                
        except Exception as e:
            logger.error(f"處理測驗回答時發生錯誤: {e}")
            return "處理您的回答時發生錯誤，請重新回答或稍後再試。"
    
    def _get_current_question_message(self, session: UserAssessmentSession) -> str:
        """獲取當前問題訊息"""
        if session.current_question_index >= len(session.questions):
            return "測驗已完成！"
        
        current_question = session.questions[session.current_question_index]
        question_num = session.current_question_index + 1
        total_questions = len(session.questions)
        
        message = f"""
📋 **問題 {question_num}/{total_questions}**

{current_question['question']}

💡 請用英語回答這個問題。
        """.strip()
        
        return message
    
    def _complete_assessment(self, session: UserAssessmentSession) -> str:
        """完成分級測驗"""
        try:
            # 標記測驗完成
            session.state = AssessmentState.COMPLETED
            session.completed_at = datetime.now()
            
            # 準備評估數據
            questions_and_responses = []
            for i, response_data in enumerate(session.responses):
                question_dict = session.questions[i]
                # 重建 AssessmentQuestion 對象
                from level_assessment_module import AssessmentQuestion
                question = AssessmentQuestion(
                    id=question_dict["id"],
                    question=question_dict["question"],
                    type=question_dict["type"],
                    expected_level=EnglishLevel(question_dict["expected_level"]),
                    scoring_criteria=question_dict["scoring_criteria"]
                )
                questions_and_responses.append((question, response_data["response"]))
            
            # 進行分級評估
            assessment_result = self.assessment_module.conduct_assessment(
                session.user_id, 
                questions_and_responses
            )
            
            # 保存用戶檔案
            self.user_profiles[session.user_id] = {
                "assessment_result": asdict(assessment_result),
                "last_updated": datetime.now().isoformat()
            }
            
            # 清理會話
            del self.active_sessions[session.user_id]
            
            # 生成結果訊息
            return self._generate_assessment_result_message(assessment_result)
            
        except Exception as e:
            logger.error(f"完成分級測驗時發生錯誤: {e}")
            return "完成測驗評估時發生錯誤，但您的回答已記錄。請稍後查看結果。"
    
    def _generate_assessment_result_message(self, result: AssessmentResult) -> str:
        """生成測驗結果訊息"""
        level_names = {
            EnglishLevel.BEGINNER: "初級 (Beginner)",
            EnglishLevel.INTERMEDIATE: "中級 (Intermediate)", 
            EnglishLevel.ADVANCED: "高級 (Advanced)"
        }
        
        level_descriptions = {
            EnglishLevel.BEGINNER: "您正在建立英語基礎，適合從日常對話和基本詞彙開始學習。",
            EnglishLevel.INTERMEDIATE: "您已有不錯的英語基礎，可以進行更複雜的對話和主題討論。",
            EnglishLevel.ADVANCED: "您的英語程度很好，可以挑戰專業話題和深度討論。"
        }
        
        message = f"""
🎉 **分級測驗完成！**

📊 **您的英語程度：{level_names[result.level]}**
📈 **總分：{result.score}/100**

{level_descriptions[result.level]}

---

💪 **您的強項：**
{self._format_skills_list(result.strengths) if result.strengths else "• 繼續努力，您會發現自己的強項！"}

📚 **建議加強：**
{self._format_skills_list(result.weaknesses) if result.weaknesses else "• 各方面表現均衡！"}

---

🎯 **為您推薦的學習主題：**
{self._format_topics_list(result.recommended_topics)}

---

✨ **準備好開始學習了嗎？**
您可以：
• 直接發送訊息開始英語對話
• 發送 /help 查看更多功能
• 發送 /topics 查看所有學習主題

Let's start your English learning journey! 🚀
        """.strip()
        
        return message
    
    def _format_skills_list(self, skills: List[str]) -> str:
        """格式化技能列表"""
        skill_translations = {
            "grammar": "語法",
            "vocabulary": "詞彙",
            "fluency": "流暢度",
            "content": "內容表達"
        }
        
        formatted_skills = []
        for skill in skills:
            translated = skill_translations.get(skill, skill)
            formatted_skills.append(f"• {translated}")
        
        return "\n".join(formatted_skills)
    
    def _format_topics_list(self, topics: List[str]) -> str:
        """格式化主題列表"""
        formatted_topics = []
        for i, topic in enumerate(topics, 1):
            formatted_topics.append(f"{i}. {topic}")
        
        return "\n".join(formatted_topics)
    
    def get_user_level(self, user_id: str) -> Optional[EnglishLevel]:
        """獲取用戶英語程度"""
        if user_id in self.user_profiles:
            result_data = self.user_profiles[user_id]["assessment_result"]
            return EnglishLevel(result_data["level"])
        return None
    
    def is_assessment_in_progress(self, user_id: str) -> bool:
        """檢查是否有進行中的測驗"""
        return (user_id in self.active_sessions and 
                self.active_sessions[user_id].state == AssessmentState.IN_PROGRESS)
    
    def reset_assessment(self, user_id: str) -> str:
        """重置分級測驗"""
        if user_id in self.active_sessions:
            del self.active_sessions[user_id]
        
        if user_id in self.user_profiles:
            del self.user_profiles[user_id]
        
        return "您的分級測驗已重置。發送 /start 開始新的測驗。"
