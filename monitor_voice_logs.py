#!/usr/bin/env python3
"""
監控語音處理日誌
即時顯示語音訊息處理狀態
"""

import time
import requests
import os

def monitor_voice_processing():
    """監控語音處理狀態"""
    
    print("🎤 語音處理監控器")
    print("=" * 50)
    print("正在監控語音處理狀態...")
    print("請在 LINE 中發送語音訊息進行測試")
    print("按 Ctrl+C 停止監控")
    print("=" * 50)
    
    # 檢查服務器狀態
    try:
        response = requests.get("http://localhost:5002/health", timeout=5)
        if response.status_code == 200:
            print("✅ LINE Bot 服務器運行正常")
        else:
            print(f"❌ 服務器狀態異常: {response.status_code}")
    except Exception as e:
        print(f"❌ 無法連接到服務器: {e}")
        return
    
    # 檢查靜態檔案目錄
    audio_dir = "static/audio"
    if os.path.exists(audio_dir):
        print(f"✅ 音檔目錄存在: {audio_dir}")
        initial_files = len(os.listdir(audio_dir))
        print(f"目前音檔數量: {initial_files}")
    else:
        print(f"❌ 音檔目錄不存在: {audio_dir}")
        return
    
    print("\n🔄 開始監控...")
    print("發送語音訊息後，這裡會顯示處理狀態")
    
    try:
        last_file_count = initial_files
        
        while True:
            time.sleep(2)  # 每2秒檢查一次
            
            # 檢查新的音檔檔案
            if os.path.exists(audio_dir):
                current_files = os.listdir(audio_dir)
                current_count = len(current_files)
                
                if current_count > last_file_count:
                    new_files = current_count - last_file_count
                    print(f"\n🎵 新增 {new_files} 個語音檔案！")
                    
                    # 顯示最新的檔案
                    current_files.sort(key=lambda x: os.path.getctime(os.path.join(audio_dir, x)), reverse=True)
                    for i, file in enumerate(current_files[:new_files]):
                        file_path = os.path.join(audio_dir, file)
                        file_size = os.path.getsize(file_path)
                        file_url = f"http://localhost:5002/audio/{file}"
                        print(f"  📁 {file}")
                        print(f"     大小: {file_size} bytes")
                        print(f"     URL: {file_url}")
                    
                    last_file_count = current_count
            
            # 簡單的狀態指示
            print(".", end="", flush=True)
            
    except KeyboardInterrupt:
        print("\n\n✅ 監控已停止")

def test_voice_url_access():
    """測試語音檔案 URL 存取"""
    
    print("\n🧪 測試語音檔案存取")
    print("=" * 50)
    
    audio_dir = "static/audio"
    if not os.path.exists(audio_dir):
        print(f"❌ 音檔目錄不存在: {audio_dir}")
        return
    
    files = os.listdir(audio_dir)
    if not files:
        print("📁 目前沒有音檔檔案")
        return
    
    # 測試最新的檔案
    files.sort(key=lambda x: os.path.getctime(os.path.join(audio_dir, x)), reverse=True)
    latest_file = files[0]
    file_url = f"http://localhost:5002/audio/{latest_file}"
    
    print(f"測試檔案: {latest_file}")
    print(f"URL: {file_url}")
    
    try:
        response = requests.head(file_url, timeout=5)
        if response.status_code == 200:
            print("✅ 語音檔案可正常存取")
            print(f"檔案類型: {response.headers.get('content-type', 'unknown')}")
            print(f"檔案大小: {response.headers.get('content-length', 'unknown')} bytes")
        else:
            print(f"❌ 語音檔案無法存取: {response.status_code}")
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

def show_voice_processing_status():
    """顯示語音處理狀態"""
    
    print("🎤 語音處理功能狀態")
    print("=" * 50)
    
    # 檢查各個組件
    components = [
        ("LINE Bot 服務器", "http://localhost:5002/health"),
        ("靜態檔案服務", "http://localhost:5002/audio/"),
    ]
    
    for name, url in components:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {name} - 正常運行")
            else:
                print(f"❌ {name} - 狀態異常 ({response.status_code})")
        except Exception as e:
            print(f"❌ {name} - 無法連接")
    
    # 檢查目錄和檔案
    audio_dir = "static/audio"
    if os.path.exists(audio_dir):
        file_count = len(os.listdir(audio_dir))
        print(f"✅ 音檔目錄 - {file_count} 個檔案")
    else:
        print(f"❌ 音檔目錄 - 不存在")
    
    print("\n📋 語音功能流程:")
    print("1. 接收 LINE 語音訊息 ✅")
    print("2. 下載語音檔案 🔄")
    print("3. 語音轉文字 (Whisper) 🔄")
    print("4. AI 生成回應 (GPT) 🔄")
    print("5. 文字轉語音 (gTTS) 🔄")
    print("6. 發送文字+語音回覆 🔄")
    
    print("\n💡 測試建議:")
    print("• 發送清晰的英語語音訊息")
    print("• 語音長度建議 3-10 秒")
    print("• 在安靜環境中錄音")
    print("• 說話速度適中")

if __name__ == "__main__":
    show_voice_processing_status()
    test_voice_url_access()
    
    print("\n" + "=" * 50)
    choice = input("是否開始監控語音處理？(y/n): ")
    
    if choice.lower() in ['y', 'yes', '是']:
        monitor_voice_processing()
    else:
        print("監控已取消")
