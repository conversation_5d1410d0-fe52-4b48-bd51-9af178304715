#!/usr/bin/env python3
"""
恢復語音功能腳本
整合重組後的語音處理模組
"""

import os
import sys
from dotenv import load_dotenv

def test_voice_modules():
    """測試語音模組導入"""
    print("🔧 測試語音模組導入...")
    
    try:
        # 添加專案根目錄到路徑
        project_root = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, project_root)
        
        # 測試語音對話模組
        from modules.voice_conversation.voice_conversation_module import VoiceConversationModule
        print("✅ VoiceConversationModule 導入成功")
        
        # 測試音檔處理模組
        from modules.audio_processing.static_audio_server import process_voice_with_static_server, init_audio_server
        print("✅ 音檔處理模組導入成功")
        
        # 測試主題管理模組
        from modules.topic_management.topic_management_module import TopicManagementModule
        print("✅ TopicManagementModule 導入成功")
        
        # 測試 OpenAI 客戶端
        from openai import OpenAI
        openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        print("✅ OpenAI 客戶端初始化成功")
        
        return True, {
            'VoiceConversationModule': VoiceConversationModule,
            'process_voice_with_static_server': process_voice_with_static_server,
            'init_audio_server': init_audio_server,
            'TopicManagementModule': TopicManagementModule,
            'openai_client': openai_client
        }
        
    except Exception as e:
        print(f"❌ 模組導入失敗: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def create_voice_enabled_bot():
    """創建支援語音功能的 LINE Bot"""
    print("\n🎤 創建語音功能完整版 LINE Bot...")
    
    # 載入環境變數
    load_dotenv()
    
    # 測試模組導入
    success, modules = test_voice_modules()
    if not success:
        print("❌ 語音模組導入失敗，無法啟動語音功能")
        return
    
    try:
        from fastapi import FastAPI, Request, HTTPException
        from fastapi.responses import JSONResponse
        from fastapi.staticfiles import StaticFiles
        from linebot.v3 import WebhookHandler
        from linebot.v3.messaging import Configuration, ApiClient, MessagingApi
        from linebot.v3.webhooks import MessageEvent, TextMessageContent, AudioMessageContent
        from linebot.v3.messaging import ReplyMessageRequest, TextMessage, AudioMessage
        from linebot.v3.exceptions import InvalidSignatureError
        import uvicorn
        from datetime import datetime
        import logging
        
        # 設定日誌
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
        
        # 初始化
        line_config = Configuration(access_token=os.getenv('LINE_CHANNEL_ACCESS_TOKEN'))
        handler = WebhookHandler(os.getenv('LINE_CHANNEL_SECRET'))
        openai_client = modules['openai_client']
        
        # 初始化語音模組
        voice_conversation = modules['VoiceConversationModule'](openai_client, None)
        topic_management = modules['TopicManagementModule']()
        
        # 設定基礎 URL (使用本地端口，稍後可更新為 ngrok HTTPS)
        port = 5004
        base_url = f"http://localhost:{port}"  # 稍後可更新為 ngrok URL
        
        # 創建 FastAPI 應用
        app = FastAPI(
            title="Emma English Learning Bot - Voice Enabled",
            description="完整語音功能的英語學習 LINE Bot",
            version="3.1.0"
        )
        
        # 靜態檔案服務
        os.makedirs("static/audio", exist_ok=True)
        app.mount("/audio", StaticFiles(directory="static/audio"), name="audio")
        
        @app.get("/")
        def root():
            return {
                "message": "Emma English Learning Bot - Voice Enabled",
                "status": "healthy",
                "version": "3.1.0",
                "features": ["voice_recognition", "voice_response", "ai_conversation"],
                "timestamp": datetime.now().isoformat()
            }
        
        @app.get("/health")
        def health():
            return {
                "status": "healthy", 
                "voice_features": "enabled",
                "modules": {
                    "voice_conversation": "✅",
                    "audio_processing": "✅", 
                    "topic_management": "✅"
                }
            }
        
        @app.post("/callback")
        async def callback(request: Request):
            signature = request.headers.get('X-Line-Signature', '')
            body = await request.body()
            body_str = body.decode('utf-8')
            
            try:
                handler.handle(body_str, signature)
                return JSONResponse(content={"status": "ok"})
            except InvalidSignatureError:
                raise HTTPException(status_code=400, detail="Invalid signature")
            except Exception as e:
                logger.error(f"Callback 錯誤: {e}")
                raise HTTPException(status_code=500, detail="Internal server error")
        
        @handler.add(MessageEvent, message=TextMessageContent)
        def handle_text_message(event):
            user_message = event.message.text.strip()
            logger.info(f"收到文字訊息: {user_message}")
            
            if user_message.lower() in ['/start', 'start']:
                response = """
🌟 Welcome to Emma English Learning Bot!

Hi! I'm Emma, your AI English teacher.

🎤 **語音功能已恢復！**
✅ 語音轉文字 (Whisper API)
✅ AI 英語對話 (GPT)
✅ 語音回覆 (gTTS)
✅ 主題式學習

📱 **使用方式：**
• 🎤 發送語音訊息 - 進行語音對話
• 💬 發送文字訊息 - 文字對話
• 📚 /topic - 獲取今日學習主題

🎯 **功能特色：**
• 真實語音識別和回應
• 個人化學習建議
• 主題式英語練習

Let's start practicing English! 😊
                """.strip()
                
            elif user_message.lower() in ['/topic', 'topic']:
                try:
                    user_id = event.source.user_id
                    daily_topic = topic_management.get_daily_topic(user_id, "intermediate")
                    response = f"""
📚 **今日學習主題**

🎯 **主題：** {daily_topic.name}
📊 **難度：** Intermediate
📝 **描述：** {daily_topic.description}

💡 **練習建議：**
• 🎤 發送語音訊息討論這個主題
• 💬 用文字分享您的想法
• 🗣️ 練習相關詞彙和表達

🌟 **開始對話：**
Try talking about this topic in English!
                    """.strip()
                except Exception as e:
                    logger.error(f"獲取主題失敗: {e}")
                    response = "Sorry, I couldn't get today's topic. Let's just have a conversation! What would you like to talk about?"
                    
            else:
                # AI 對話
                try:
                    ai_response = openai_client.chat.completions.create(
                        model="gpt-3.5-turbo",
                        messages=[
                            {"role": "system", "content": "你是 Emma，一位友善的英語老師。用英語回應學生，保持鼓勵和支持。"},
                            {"role": "user", "content": user_message}
                        ],
                        max_tokens=200,
                        temperature=0.7
                    )
                    response = ai_response.choices[0].message.content.strip()
                except Exception as e:
                    logger.error(f"AI 回應失敗: {e}")
                    response = "Hello! I'm having some technical difficulties. Let's try again! 😊"
            
            # 發送回覆
            try:
                with ApiClient(line_config) as api_client:
                    line_bot_api = MessagingApi(api_client)
                    line_bot_api.reply_message(
                        ReplyMessageRequest(
                            reply_token=event.reply_token,
                            messages=[TextMessage(text=response)]
                        )
                    )
                logger.info("✅ 文字回覆發送成功")
            except Exception as e:
                logger.error(f"❌ 發送文字回覆失敗: {e}")
        
        @handler.add(MessageEvent, message=AudioMessageContent)
        def handle_audio_message(event):
            """處理語音訊息 - 完整語音功能"""
            user_id = event.source.user_id
            message_id = event.message.id
            duration = event.message.duration
            
            logger.info(f"🎤 收到語音訊息: {message_id} (時長: {duration}ms)")
            
            try:
                # 獲取語音檔案 URL
                audio_url = f"https://api-data.line.me/v2/bot/message/{message_id}/content"
                
                # 獲取當前主題
                default_level = "intermediate"
                daily_topic = topic_management.get_daily_topic(user_id, default_level)
                
                # 語音轉文字處理
                logger.info("📥 開始語音處理...")
                audio_file = voice_conversation.download_voice_message(message_id, audio_url)
                
                if audio_file and os.path.exists(audio_file):
                    file_size = os.path.getsize(audio_file)
                    logger.info(f"✅ 語音檔案下載成功: {file_size} bytes")
                    
                    if file_size > 0:
                        # 使用 Whisper API 進行語音轉文字
                        transcription = voice_conversation.transcribe_audio(audio_file)
                        
                        if transcription and transcription.text:
                            user_text = transcription.text.strip()
                            logger.info(f"✅ 語音轉文字成功: '{user_text}'")
                            
                            # 使用完整的語音處理系統
                            feedback_text, voice_url = modules['process_voice_with_static_server'](
                                openai_client, user_text, daily_topic.name, default_level, base_url
                            )
                            
                            # 準備回覆訊息
                            messages = [TextMessage(text=feedback_text)]
                            
                            # 添加語音訊息
                            if voice_url:
                                try:
                                    audio_message = AudioMessage(
                                        original_content_url=voice_url,
                                        duration=5000
                                    )
                                    messages.append(audio_message)
                                    logger.info(f"✅ 語音回覆已準備: {voice_url}")
                                except Exception as audio_error:
                                    logger.error(f"❌ 創建語音訊息失敗: {audio_error}")
                            
                            response_messages = messages
                        else:
                            response_messages = [TextMessage(text="Sorry, I couldn't understand your voice clearly. Please try again in a quiet environment.")]
                    else:
                        response_messages = [TextMessage(text="The audio file seems to be empty. Please try recording again.")]
                else:
                    response_messages = [TextMessage(text="I couldn't download your voice message. Please try again.")]
                
                # 發送回覆
                with ApiClient(line_config) as api_client:
                    line_bot_api = MessagingApi(api_client)
                    line_bot_api.reply_message(
                        ReplyMessageRequest(
                            reply_token=event.reply_token,
                            messages=response_messages
                        )
                    )
                logger.info("✅ 語音回覆發送成功")
                
            except Exception as e:
                logger.error(f"❌ 語音處理失敗: {e}")
                import traceback
                traceback.print_exc()
                
                # 備用回覆
                try:
                    with ApiClient(line_config) as api_client:
                        line_bot_api = MessagingApi(api_client)
                        line_bot_api.reply_message(
                            ReplyMessageRequest(
                                reply_token=event.reply_token,
                                messages=[TextMessage(text=f"語音處理遇到問題: {str(e)}\n請稍後再試或使用文字訊息。")]
                            )
                        )
                    logger.info("✅ 錯誤回覆發送成功")
                except Exception as backup_error:
                    logger.error(f"❌ 備用回覆也失敗: {backup_error}")
        
        print("✅ 語音功能完整版 LINE Bot 創建成功")
        print("🎤 所有語音功能已啟用：")
        print("  • Whisper API 語音轉文字")
        print("  • GPT AI 對話生成")
        print("  • gTTS 語音回覆")
        print("  • 主題式學習")
        
        # 使用端口 5004 避免衝突
        port = 5004
        print(f"\n🚀 啟動語音功能完整版服務 (端口: {port})...")
        print(f"🌐 服務 URL: http://localhost:{port}")
        print(f"🔗 健康檢查: http://localhost:{port}/health")
        print("\n💡 提示：")
        print(f"• 更新 ngrok: ngrok http {port}")
        print(f"• 更新 LINE Webhook: https://your-ngrok-url/callback")
        
        uvicorn.run(app, host="0.0.0.0", port=port, log_level="info")
        
    except Exception as e:
        print(f"❌ 創建語音功能 LINE Bot 失敗: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函數"""
    print("🎤 Emma 英語學習 LINE Bot - 語音功能恢復")
    print("=" * 60)
    
    # 載入環境變數
    load_dotenv()
    
    # 檢查環境變數
    required_vars = ["LINE_CHANNEL_ACCESS_TOKEN", "LINE_CHANNEL_SECRET", "OPENAI_API_KEY"]
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少環境變數: {', '.join(missing_vars)}")
        return
    
    print("✅ 環境變數檢查通過")
    
    # 創建語音功能完整版
    create_voice_enabled_bot()

if __name__ == "__main__":
    main()
