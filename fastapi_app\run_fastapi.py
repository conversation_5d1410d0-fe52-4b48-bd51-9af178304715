#!/usr/bin/env python3
"""
FastAPI 應用程式啟動腳本
"""

import uvicorn
import sys
import os

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastapi_app.core.config import get_settings

def main():
    """主啟動函數"""
    settings = get_settings()
    
    print(f"🚀 啟動 {settings.app_name}")
    print(f"📍 伺服器地址: http://{settings.host}:{settings.port}")
    print(f"📚 API 文檔: http://{settings.host}:{settings.port}{settings.docs_url}")
    print(f"🔧 除錯模式: {'開啟' if settings.debug else '關閉'}")
    
    uvicorn.run(
        "fastapi_app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True
    )

if __name__ == "__main__":
    main()
