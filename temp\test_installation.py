#!/usr/bin/env python3
"""
測試安裝是否成功
"""

import sys
import os

def test_python_version():
    """測試 Python 版本"""
    print("🐍 檢查 Python 版本...")
    version = sys.version_info
    print(f"Python 版本: {version.major}.{version.minor}.{version.micro}")
    
    if version < (3, 8):
        print("❌ 需要 Python 3.8 或更高版本")
        return False
    else:
        print("✅ Python 版本符合要求")
        return True

def test_package_imports():
    """測試套件導入"""
    print("\n📦 檢查套件安裝...")
    
    packages = [
        ("fastapi", "FastAPI"),
        ("uvicorn", "Uvicorn"),
        ("pydantic", "Pydantic"),
        ("requests", "Requests"),
        ("openai", "OpenAI"),
        ("linebot", "Line Bot SDK")
    ]
    
    success_count = 0
    for package, name in packages:
        try:
            __import__(package)
            print(f"✅ {name} - 已安裝")
            success_count += 1
        except ImportError:
            print(f"❌ {name} - 未安裝")
    
    print(f"\n套件安裝狀態: {success_count}/{len(packages)}")
    return success_count == len(packages)

def test_optional_packages():
    """測試可選套件"""
    print("\n🔧 檢查可選套件...")
    
    optional_packages = [
        ("speech_recognition", "語音識別"),
        ("pydub", "音頻處理"),
        ("pytest", "測試框架")
    ]
    
    for package, name in optional_packages:
        try:
            __import__(package)
            print(f"✅ {name} - 已安裝")
        except ImportError:
            print(f"⚠️ {name} - 未安裝 (可選)")

def test_environment_variables():
    """測試環境變數"""
    print("\n🔑 檢查環境變數...")
    
    required_vars = [
        "LINE_CHANNEL_ACCESS_TOKEN",
        "LINE_CHANNEL_SECRET", 
        "OPENAI_API_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var} - 已設定")
        else:
            print(f"❌ {var} - 未設定")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n⚠️ 缺少環境變數: {', '.join(missing_vars)}")
        print("請設定環境變數或編輯 .env 文件")
        return False
    else:
        print("\n✅ 所有環境變數已設定")
        return True

def test_file_structure():
    """測試文件結構"""
    print("\n📁 檢查文件結構...")
    
    required_files = [
        "autogen_line_app/simple_line_bot.py",
        "run_simple_line_bot.py",
        "install_dependencies.py",
        ".env.example"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} - 存在")
        else:
            print(f"❌ {file_path} - 不存在")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def test_simple_import():
    """測試簡化版 Bot 導入"""
    print("\n🤖 測試 Bot 導入...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from autogen_line_app.simple_line_bot import SimpleLineBot
        print("✅ SimpleLineBot 導入成功")
        return True
    except ImportError as e:
        print(f"❌ SimpleLineBot 導入失敗: {e}")
        return False
    except Exception as e:
        print(f"⚠️ SimpleLineBot 導入警告: {e}")
        return True

def main():
    """主測試函數"""
    print("🧪 AutoGen Line Bot 安裝測試")
    print("=" * 50)
    
    tests = [
        ("Python 版本", test_python_version),
        ("套件安裝", test_package_imports),
        ("文件結構", test_file_structure),
        ("Bot 導入", test_simple_import),
        ("環境變數", test_environment_variables)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 測試失敗: {e}")
            results.append((test_name, False))
    
    # 測試可選套件
    test_optional_packages()
    
    # 總結
    print("\n" + "=" * 50)
    print("📊 測試結果總結:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n通過率: {passed}/{len(results)} ({passed/len(results)*100:.1f}%)")
    
    if passed == len(results):
        print("\n🎉 所有測試通過！可以啟動 Line Bot")
        print("執行: python run_simple_line_bot.py")
    else:
        print("\n⚠️ 部分測試失敗，請檢查上述問題")
        print("建議執行: python install_dependencies.py")

if __name__ == "__main__":
    main()
