# AutoGen Line 英語口說學習 Bot

這是一個使用 AutoGen 多代理框架建立的 Line 英語口說學習應用程式，結合了 AI 對話、語音處理和個人化學習功能。

## 🎯 專案特色

### AutoGen 多代理協作
- **Agent1 (Developer)**: 負責程式碼開發和系統架構設計
- **Agent2 (Validator)**: 負責程式碼品質驗證和安全檢查
- **Agent3 (Optimizer)**: 負責程式碼優化和性能改進

### 核心功能
- 🗣️ **語音對話練習**: 支援語音輸入和 AI 回饋
- 🎯 **個人化學習**: 根據程度調整對話難度
- 📊 **進度追蹤**: 記錄學習成果和詞彙量
- 🤖 **多代理協作**: AutoGen 框架驅動的智能對話
- 📱 **Line 整合**: 無縫的 Line Bot 體驗

## 📁 專案結構

```
mylineenglishbot/
├── autogen_line_app/            # AutoGen 多代理應用程式
│   ├── __init__.py
│   ├── agent1_developer.py      # Agent1: 程式碼開發代理
│   ├── agent2_validator.py      # Agent2: 程式碼驗證代理
│   ├── agent3_optimizer.py     # Agent3: 程式碼優化代理
│   ├── autogen_collaboration.py # AutoGen 協作系統
│   ├── line_english_bot.py      # 最終 Line Bot 實現
│   └── tests/                   # 測試文件
│       ├── __init__.py
│       └── test_autogen_agents.py
├── fastapi_app/                 # FastAPI 應用程式 (原有)
│   ├── main.py
│   ├── api/
│   ├── models/
│   └── services/
├── linegptbot/                  # 原有的 Line Bot 應用程式
│   └── app.py
├── run_autogen_line_bot.py      # AutoGen Line Bot 啟動腳本
├── run_autogen_collaboration.py # AutoGen 協作流程腳本
├── requirements.txt             # Python 依賴 (已更新)
├── .env.example                # 環境變數範例 (已更新)
└── README.md                   # 專案說明
```

## 🚀 AutoGen 多代理協作流程

### Agent1: 程式碼開發代理
- 🔧 設計 Line 英語學習 Bot 架構
- 🎯 整合 AutoGen 多代理系統
- 🗣️ 實現語音處理功能
- 📱 建立 Line Bot 整合
- 🏗️ 設計可擴展的系統架構

### Agent2: 程式碼驗證代理
- 🔍 審查程式碼品質和結構
- ⚠️ 識別潛在錯誤和安全問題
- 📋 檢查程式碼規範和最佳實踐
- 🧪 驗證功能完整性
- 💡 提供改進建議

### Agent3: 程式碼優化代理
- ⚡ 優化程式碼性能和效率
- 🔧 重構和改進程式碼結構
- 🛡️ 增強錯誤處理和安全性
- 📚 完善文檔和註釋
- 🎯 整合前兩個代理的建議

## 🌟 英語學習功能

### 核心學習模組
- 📊 **分級測驗**: 自動評估學員英語程度
- 🗣️ **語音對話**: 即時語音識別和回饋
- 📚 **詞彙擴展**: 個人化詞彙學習計劃
- 🎯 **主題對話**: 根據程度推薦對話主題
- 📈 **進度追蹤**: 詳細的學習成果記錄

### AI 教師功能
- 👩‍🏫 **Emma (英語老師)**: 友善的對話練習指導
- 🔍 **Alex (對話管理)**: 學習進度和主題管理
- 📊 **Dr. Smith (評估專家)**: 能力評估和學習建議

## 🛠️ 安裝和設定

### 1. 安裝依賴

```bash
pip install -r requirements.txt
```

### 2. 環境變數設定

複製 `.env.example` 為 `.env` 並設定必要的 API 金鑰：

```bash
cp .env.example .env
```

編輯 `.env` 文件，設定以下變數：
```bash
# Line Bot 設定
LINE_CHANNEL_ACCESS_TOKEN=your_line_channel_access_token
LINE_CHANNEL_SECRET=your_line_channel_secret

# OpenAI 設定
OPENAI_API_KEY=your_openai_api_key

# 可選：語音處理設定
AZURE_SPEECH_KEY=your_azure_speech_key
AZURE_SPEECH_REGION=eastus
```

### 3. 啟動應用程式

#### 🚀 推薦方法：一鍵安裝和啟動
```bash
# Python 版本 (推薦，避免編碼問題)
python setup_and_run.py

# Windows 批次文件 (簡化版)
quick_install.bat

# PowerShell 版本
powershell -ExecutionPolicy Bypass -File install_and_run.ps1
```

#### 方法一：手動安裝依賴
```bash
# 安裝依賴
python install_dependencies.py

# 啟動簡化版 Line Bot (推薦)
python run_simple_line_bot.py
```

#### 方法二：啟動完整版 AutoGen Line Bot
```bash
python run_autogen_line_bot.py
```

#### 方法三：執行 AutoGen 協作流程
```bash
python run_autogen_collaboration.py
```

#### 方法四：啟動原有的 FastAPI 應用
```bash
python run_fastapi.py
```

### 4. 訪問應用程式

- **Line Bot Webhook**: http://localhost:8000/webhook
- **API 文檔**: http://localhost:8000/docs
- **健康檢查**: http://localhost:8000/health
- **學員檔案 API**: http://localhost:8000/api/student/{user_id}/profile

## 🔧 故障排除

### 常見問題

#### 1. 批次文件編碼錯誤
```bash
# 問題：執行 .bat 文件出現亂碼或命令錯誤
# 解決方案：使用 Python 版本的安裝腳本
python setup_and_run.py

# 或使用簡化版批次文件
quick_install.bat
```

#### 2. 導入錯誤 "無法解析匯入 fastapi"
```bash
# 解決方案：安裝缺少的套件
python install_dependencies.py

# 或手動安裝
pip install fastapi uvicorn line-bot-sdk openai
```

#### 3. 環境變數未設定
```bash
# 複製範例文件
cp .env.example .env

# 編輯 .env 文件，設定你的 API 金鑰
# LINE_CHANNEL_ACCESS_TOKEN=your_token
# LINE_CHANNEL_SECRET=your_secret
# OPENAI_API_KEY=your_key
```

#### 4. AutoGen 套件安裝失敗
```bash
# 嘗試使用簡化版本
python run_simple_line_bot.py

# 或手動安裝 AutoGen
pip install autogen-agentchat autogen-ext[openai]
```

#### 5. 語音功能不可用
```bash
# 安裝語音處理套件
pip install SpeechRecognition pydub
```

### 版本選擇

- **簡化版** (`simple_line_bot.py`): 基本功能，依賴較少，適合快速測試
- **完整版** (`line_english_bot.py`): 包含 AutoGen 多代理功能，功能完整

## 🧪 測試

### 執行 AutoGen 代理測試
```bash
pytest autogen_line_app/tests/test_autogen_agents.py -v
```

### 執行 FastAPI 測試
```bash
pytest fastapi_app/tests/ -v
```

### 測試 AutoGen 協作流程
```bash
python run_autogen_collaboration.py
```

## 📱 Line Bot 使用指南

### 基本命令
- `/start` - 開始學習旅程
- `/help` - 顯示幫助訊息
- `/level` - 進行英語程度評估

### 學習流程
1. **首次使用**: 發送 `/start` 開始程度評估
2. **語音練習**: 發送語音訊息進行口說練習
3. **文字對話**: 發送文字訊息進行對話練習
4. **進度查看**: 系統自動追蹤學習進度

### 功能示例
```
用戶: /start
Bot: 🌟 Welcome to English Learning Bot!
     Let's assess your English level...

用戶: [語音訊息] "Hello, my name is John..."
Bot: Great pronunciation! Here's some feedback...
     📖 New vocabulary: pronunciation, feedback
     🎯 Grammar tip: Use "My name is" for introductions

用戶: What topics can we discuss?
Bot: Based on your intermediate level, I recommend:
     • Travel experiences
     • Work and career
     • Hobbies and interests
     Which topic interests you most?
```

## 🔧 AutoGen 代理開發指南

### 擴展代理功能

#### 添加新的 Agent
1. 在 `autogen_line_app/` 中建立新的代理文件
2. 繼承 AutoGen 的 `AssistantAgent` 類別
3. 定義代理的系統訊息和工具
4. 在協作系統中註冊新代理

#### 自定義學習模組
1. 在 `line_english_bot.py` 中添加新的處理方法
2. 實現特定的學習邏輯
3. 整合到多代理協作流程中

### 配置管理

所有配置都在 `Config` 類別中管理，支援環境變數覆蓋。

## 🏗️ 技術架構

### 核心技術棧
- **AutoGen**: 多代理協作框架
- **FastAPI**: 現代 Web 框架
- **Line Bot SDK**: Line 機器人開發
- **OpenAI GPT-4**: AI 對話引擎
- **Speech Recognition**: 語音識別
- **Pydantic**: 資料驗證
- **Uvicorn**: ASGI 伺服器

### 系統架構圖
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Line User     │───▶│   Line Bot      │───▶│  AutoGen Team   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                         │
                              ▼                         ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Speech Processor│    │   OpenAI API    │
                       └─────────────────┘    └─────────────────┘
                              │                         │
                              ▼                         ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │  Data Manager   │    │  Learning Data  │
                       └─────────────────┘    └─────────────────┘
```

## 🤝 貢獻指南

1. Fork 此專案
2. 建立功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交變更 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 開啟 Pull Request

## 📄 授權

此專案使用 MIT 授權條款。詳見 [LICENSE](LICENSE) 文件。

## 🙏 致謝

- [Microsoft AutoGen](https://github.com/microsoft/autogen) - 多代理協作框架
- [Line Developers](https://developers.line.biz/) - Line Bot 平台
- [OpenAI](https://openai.com/) - GPT-4 AI 模型
- [FastAPI](https://fastapi.tiangolo.com/) - 現代 Web 框架

---

**由 AutoGen 多代理系統協作開發** 🤖✨
