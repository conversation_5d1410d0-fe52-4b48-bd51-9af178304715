#!/usr/bin/env python3
"""
測試語音回饋功能
"""

import os
from dotenv import load_dotenv
from openai import OpenAI
from voice_feedback_simple import process_voice_simple

# 載入環境變數
load_dotenv()

def test_voice_feedback():
    """測試語音回饋功能"""
    
    # 初始化 OpenAI 客戶端
    openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    
    # 測試案例
    test_cases = [
        {
            "text": "Would you please discuss to me?",
            "topic": "自我介紹",
            "level": "beginner"
        },
        {
            "text": "I like to travel and see new places",
            "topic": "旅遊",
            "level": "intermediate"
        },
        {
            "text": "Technology has changed our lives significantly",
            "topic": "科技",
            "level": "advanced"
        }
    ]
    
    print("🧪 測試語音回饋功能")
    print("=" * 50)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📝 測試案例 {i}:")
        print(f"文字: {case['text']}")
        print(f"主題: {case['topic']}")
        print(f"程度: {case['level']}")
        print("-" * 30)
        
        try:
            feedback = process_voice_simple(
                openai_client, 
                case['text'], 
                case['topic'], 
                case['level']
            )
            print("✅ 回饋結果:")
            print(feedback)
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
        
        print("\n" + "=" * 50)

if __name__ == "__main__":
    test_voice_feedback()
