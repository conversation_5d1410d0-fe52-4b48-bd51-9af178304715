@echo off
echo ========================================
echo Quick Install for AutoGen Line Bot
echo ========================================

echo.
echo Step 1: Check Python
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python not found
    echo Please install Python 3.8+ first
    pause
    exit /b 1
)

echo.
echo Step 2: Install packages
pip install fastapi uvicorn line-bot-sdk openai requests pydantic

echo.
echo Step 3: Test installation
python test_installation.py

echo.
echo Step 4: Setup environment
if not exist .env (
    copy .env.example .env
    echo Please edit .env file with your API keys
    notepad .env
)

echo.
echo Step 5: Start bot
python run_simple_line_bot.py

pause
