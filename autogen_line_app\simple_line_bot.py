"""
簡化版 Line 英語學習 Bot
解決導入問題的版本
"""

import logging
import os
import tempfile
from datetime import datetime
from typing import Any, Dict, List, Optional

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 檢查並導入必要的套件
def check_and_import():
    """檢查並導入必要的套件"""
    missing_packages = []
    
    try:
        global FastAPI, HTTPException, Request, JSONResponse
        from fastapi import FastAPI, HTTPException, Request
        from fastapi.responses import JSONResponse
        logger.info("✅ FastAPI 導入成功")
    except ImportError:
        missing_packages.append("fastapi")
        logger.error("❌ FastAPI 未安裝")
    
    try:
        global WebhookHandler, InvalidSignatureError
        global Configuration, MessagingApi, ReplyMessageRequest, TextMessage
        global MessageEvent, TextMessageContent, AudioMessageContent, ApiClient
        
        from linebot.v3 import WebhookHandler
        from linebot.v3.exceptions import InvalidSignatureError
        from linebot.v3.messaging import (
            Configuration, MessagingApi, ReplyMessageRequest, TextMessage, ApiClient
        )
        from linebot.v3.webhooks import MessageEvent, TextMessageContent, AudioMessageContent
        logger.info("✅ Line Bot SDK 導入成功")
    except ImportError:
        missing_packages.append("line-bot-sdk")
        logger.error("❌ Line Bot SDK 未安裝")
    
    try:
        global openai
        import openai
        logger.info("✅ OpenAI 導入成功")
    except ImportError:
        missing_packages.append("openai")
        logger.error("❌ OpenAI 未安裝")
    
    if missing_packages:
        logger.error(f"缺少套件: {', '.join(missing_packages)}")
        logger.info("請執行: python install_dependencies.py")
        return False
    
    return True

class SimpleConfig:
    """簡化的配置類別"""
    
    def __init__(self):
        self.line_channel_access_token = os.getenv('LINE_CHANNEL_ACCESS_TOKEN')
        self.line_channel_secret = os.getenv('LINE_CHANNEL_SECRET')
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        
        if not all([self.line_channel_access_token, self.line_channel_secret, self.openai_api_key]):
            logger.error("❌ 環境變數未設定完整")
            logger.info("請設定: LINE_CHANNEL_ACCESS_TOKEN, LINE_CHANNEL_SECRET, OPENAI_API_KEY")

class SimpleStudentProfile:
    """簡化的學員檔案"""
    
    def __init__(self, user_id: str):
        self.user_id = user_id
        self.name = ""
        self.english_level = "beginner"
        self.conversation_count = 0
        self.vocabulary_count = 0
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
    
    def update_progress(self):
        """更新進度"""
        self.conversation_count += 1
        self.updated_at = datetime.now()

class SimpleAITeacher:
    """簡化的 AI 老師"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        openai.api_key = api_key
    
    async def get_response(self, user_message: str, user_level: str = "beginner") -> str:
        """獲取 AI 回應"""
        try:
            system_message = f"""
            你是一位友善的英語老師 Emma。學員的英語程度是 {user_level}。
            請用英語回應，並提供學習建議。保持鼓勵和支持的態度。
            """
            
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                max_tokens=500,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"AI 回應錯誤: {e}")
            return "Sorry, I'm having some technical difficulties. Let's try again! 😊"

class SimpleLineBot:
    """簡化的 Line Bot"""
    
    def __init__(self):
        if not check_and_import():
            raise ImportError("缺少必要的套件")
        
        self.config = SimpleConfig()
        
        # 初始化 Line Bot
        self.line_config = Configuration(access_token=self.config.line_channel_access_token)
        self.handler = WebhookHandler(self.config.line_channel_secret)
        
        # 初始化 AI 老師
        self.ai_teacher = SimpleAITeacher(self.config.openai_api_key)
        
        # 學員資料存儲
        self.student_profiles: Dict[str, SimpleStudentProfile] = {}
        
        # 初始化 FastAPI
        self.app = FastAPI(
            title="Simple Line English Bot",
            description="簡化版英語學習 Bot",
            version="1.0.0"
        )
        
        self._setup_routes()
        self._setup_handlers()
        
        logger.info("✅ Simple Line Bot 初始化完成")
    
    def _setup_routes(self):
        """設定路由"""
        
        @self.app.get("/")
        async def root():
            return {
                "message": "Simple Line English Bot is running!",
                "version": "1.0.0",
                "status": "healthy"
            }
        
        @self.app.get("/health")
        async def health_check():
            return {"status": "healthy", "timestamp": datetime.now().isoformat()}
        
        @self.app.post("/webhook")
        async def webhook(request: Request):
            """Line Webhook 處理"""
            signature = request.headers.get('X-Line-Signature', '')
            body = await request.body()
            
            try:
                self.handler.handle(body.decode('utf-8'), signature)
                return JSONResponse(content={"status": "ok"})
            except InvalidSignatureError:
                logger.error("無效的 Line 簽名")
                raise HTTPException(status_code=400, detail="Invalid signature")
            except Exception as e:
                logger.error(f"Webhook 處理錯誤: {e}")
                raise HTTPException(status_code=500, detail="Internal server error")
    
    def _setup_handlers(self):
        """設定事件處理器"""
        
        @self.handler.add(MessageEvent, message=TextMessageContent)
        async def handle_text_message(event):
            """處理文字訊息"""
            try:
                await self._process_text_message(event)
            except Exception as e:
                logger.error(f"處理文字訊息錯誤: {e}")
                await self._send_error_message(event.reply_token)
    
    async def _process_text_message(self, event):
        """處理文字訊息"""
        user_id = event.source.user_id
        user_message = event.message.text.strip()
        
        # 獲取或建立學員檔案
        if user_id not in self.student_profiles:
            self.student_profiles[user_id] = SimpleStudentProfile(user_id)
        
        profile = self.student_profiles[user_id]
        
        # 特殊命令處理
        if user_message.lower() in ['/start', '開始', 'start']:
            response = self._get_welcome_message(profile)
        elif user_message.lower() in ['/help', '幫助', 'help']:
            response = self._get_help_message()
        else:
            # 使用 AI 老師回應
            response = await self.ai_teacher.get_response(user_message, profile.english_level)
            profile.update_progress()
        
        # 發送回覆
        await self._send_text_reply(event.reply_token, response)
    
    def _get_welcome_message(self, profile: SimpleStudentProfile) -> str:
        """獲取歡迎訊息"""
        return f"""
🌟 Welcome to English Learning Bot!

Hi! I'm Emma, your AI English teacher. 

Your current level: {profile.english_level.title()}
Conversations: {profile.conversation_count}

Let's practice English together! You can:
• Send me any message to start a conversation
• Ask questions about English
• Practice speaking (coming soon!)

What would you like to talk about today? 😊
        """.strip()
    
    def _get_help_message(self) -> str:
        """獲取幫助訊息"""
        return """
📚 How to use English Learning Bot:

🗣️ **Text Chat**: Send me messages in English for practice
💬 **Commands**:
   • /start - Begin your learning journey
   • /help - Show this help message

🌟 **Features**:
   • Personalized conversation practice
   • Grammar and vocabulary help
   • Encouraging feedback
   • Progress tracking

Ready to practice? Just start talking! 😊
        """.strip()
    
    async def _send_text_reply(self, reply_token: str, text: str):
        """發送文字回覆"""
        try:
            with ApiClient(self.line_config) as api_client:
                line_bot_api = MessagingApi(api_client)
                line_bot_api.reply_message(
                    ReplyMessageRequest(
                        reply_token=reply_token,
                        messages=[TextMessage(text=text)]
                    )
                )
        except Exception as e:
            logger.error(f"發送回覆錯誤: {e}")
    
    async def _send_error_message(self, reply_token: str):
        """發送錯誤訊息"""
        error_message = "Sorry, I encountered an error. Please try again later. 😔"
        await self._send_text_reply(reply_token, error_message)
    
    def run(self, host: str = "0.0.0.0", port: int = 8000):
        """啟動 Bot"""
        try:
            import uvicorn
            logger.info(f"🚀 啟動 Simple Line English Bot")
            logger.info(f"📍 服務地址: http://{host}:{port}")
            uvicorn.run(self.app, host=host, port=port, log_level="info")
        except ImportError:
            logger.error("❌ uvicorn 未安裝，請執行: pip install uvicorn")
            raise

def main():
    """主程式"""
    try:
        bot = SimpleLineBot()
        bot.run()
    except Exception as e:
        logger.error(f"應用程式啟動失敗: {e}")
        raise

if __name__ == "__main__":
    main()
