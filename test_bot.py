#!/usr/bin/env python3
"""
測試 Line Bot 啟動
"""

import os
import sys

def main():
    print("🤖 測試 Line Bot 啟動")
    print("=" * 30)
    
    # 檢查環境變數
    print("\n📋 檢查環境變數:")
    env_vars = ["LINE_CHANNEL_ACCESS_TOKEN", "LINE_CHANNEL_SECRET", "OPENAI_API_KEY"]
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value[:10]}...")
        else:
            print(f"❌ {var}: 未設定")
    
    # 測試導入
    print("\n📦 測試套件導入:")
    
    try:
        import fastapi
        print("✅ FastAPI 導入成功")
    except ImportError:
        print("❌ FastAPI 導入失敗")
        return
    
    try:
        import uvicorn
        print("✅ Uvicorn 導入成功")
    except ImportError:
        print("❌ Uvicorn 導入失敗")
        return
    
    try:
        from linebot.v3 import WebhookHandler
        print("✅ Line Bot SDK 導入成功")
    except ImportError:
        print("❌ Line Bot SDK 導入失敗")
        return
    
    try:
        import openai
        print("✅ OpenAI 導入成功")
    except ImportError:
        print("❌ OpenAI 導入失敗")
        return
    
    # 建立簡單的 FastAPI 應用
    print("\n🚀 建立 FastAPI 應用:")
    
    try:
        from fastapi import FastAPI
        app = FastAPI(title="Test Line Bot")
        
        @app.get("/")
        def root():
            return {"message": "Line Bot is running!"}
        
        @app.get("/health")
        def health():
            return {"status": "healthy"}
        
        print("✅ FastAPI 應用建立成功")
        
        # 啟動服務
        print("\n🌐 啟動服務:")
        print("服務地址: http://localhost:8000")
        print("API 文檔: http://localhost:8000/docs")
        print("按 Ctrl+C 停止服務")
        
        uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
        
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 服務已停止")
    except Exception as e:
        print(f"💥 錯誤: {e}")
        sys.exit(1)
