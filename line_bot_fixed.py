#!/usr/bin/env python3
"""
修正版 Line Bot - 包含正確的 webhook 端點
"""

import os
import logging
from datetime import datetime
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    print("啟動 Line English Bot")
    print("=" * 40)
    
    # 檢查環境變數
    required_vars = ["LINE_CHANNEL_ACCESS_TOKEN", "LINE_CHANNEL_SECRET", "OPENAI_API_KEY"]
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value[:10]}...")
        else:
            print(f"❌ {var}: 未設定")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n❌ 缺少環境變數: {', '.join(missing_vars)}")
        return
    
    # 導入套件
    try:
        from fastapi import FastAPI, Request, HTTPException
        from fastapi.responses import JSONResponse
        import uvicorn
        from linebot.v3 import WebhookHandler
        from linebot.v3.exceptions import InvalidSignatureError
        from linebot.v3.messaging import Configuration, MessagingApi, ReplyMessageRequest, TextMessage, ApiClient
        from linebot.v3.webhooks import MessageEvent, TextMessageContent
        import openai
        
        print("所有套件導入成功")
        
    except ImportError as e:
        print(f"❌ 套件導入失敗: {e}")
        return
    
    # 設定 Line Bot
    line_config = Configuration(access_token=os.getenv('LINE_CHANNEL_ACCESS_TOKEN'))
    handler = WebhookHandler(os.getenv('LINE_CHANNEL_SECRET'))
    
    # 設定 OpenAI
    openai.api_key = os.getenv('OPENAI_API_KEY')
    
    # 建立 FastAPI 應用
    app = FastAPI(
        title="Line English Learning Bot",
        description="英語學習 Line Bot",
        version="1.0.0"
    )
    
    # 學員資料存儲
    student_profiles = {}
    
    # 根路由
    @app.get("/")
    def root():
        return {
            "message": "Line English Learning Bot is running!",
            "status": "healthy",
            "timestamp": datetime.now().isoformat()
        }
    
    # 健康檢查
    @app.get("/health")
    def health_check():
        return {"status": "healthy"}
    
    # Line Webhook 端點 - 支援多個路徑
    @app.post("/webhook")
    async def webhook(request: Request):
        """處理 Line Webhook"""
        signature = request.headers.get('X-Line-Signature', '')
        body = await request.body()

        try:
            handler.handle(body.decode('utf-8'), signature)
            return JSONResponse(content={"status": "ok"})
        except InvalidSignatureError:
            logger.error("無效的 Line 簽名")
            raise HTTPException(status_code=400, detail="Invalid signature")
        except Exception as e:
            logger.error(f"Webhook 處理錯誤: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")

    # Line Callback 端點 (備用路徑)
    @app.post("/callback")
    async def callback(request: Request):
        """處理 Line Callback (備用路徑)"""
        # 詳細日誌記錄
        logger.info("=== 收到 Line Callback 請求 ===")

        # 獲取簽名
        signature = request.headers.get('X-Line-Signature', '')
        logger.info(f"X-Line-Signature: {signature[:20]}..." if signature else "X-Line-Signature: 未提供")

        # 獲取請求體
        body = await request.body()
        body_str = body.decode('utf-8')
        logger.info(f"Request body length: {len(body_str)}")
        logger.info(f"Request body preview: {body_str[:200]}...")

        # 檢查必要的標頭
        if not signature:
            logger.error("❌ 缺少 X-Line-Signature 標頭")
            raise HTTPException(status_code=400, detail="Missing X-Line-Signature header")

        try:
            # 嘗試處理 webhook
            handler.handle(body_str, signature)
            logger.info("✅ Webhook 處理成功")
            return JSONResponse(content={"status": "ok"})

        except InvalidSignatureError as e:
            logger.error(f"❌ 無效的 Line 簽名: {e}")
            logger.error(f"Channel Secret: {os.getenv('LINE_CHANNEL_SECRET')[:10]}...")
            raise HTTPException(status_code=400, detail="Invalid signature")

        except Exception as e:
            logger.error(f"❌ Callback 處理錯誤: {e}")
            logger.error(f"錯誤類型: {type(e).__name__}")
            import traceback
            logger.error(f"完整錯誤: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail="Internal server error")
    
    # AI 回應函數
    async def get_ai_response(user_message: str, user_level: str = "beginner") -> str:
        """獲取 AI 回應"""
        try:
            system_message = f"""
            你是一位友善的英語老師 Emma。學員的英語程度是 {user_level}。
            請用英語回應，並提供學習建議。保持鼓勵和支持的態度。
            如果學員用中文提問，可以適當使用中文解釋。
            """
            
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                max_tokens=500,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"AI 回應錯誤: {e}")
            return "Sorry, I'm having some technical difficulties. Let's try again! 😊"
    
    # Line 訊息處理
    @handler.add(MessageEvent, message=TextMessageContent)
    async def handle_text_message(event):
        """處理文字訊息"""
        user_id = event.source.user_id
        user_message = event.message.text.strip()
        
        logger.info(f"收到訊息: {user_message} (來自: {user_id})")
        
        # 初始化學員檔案
        if user_id not in student_profiles:
            student_profiles[user_id] = {
                "user_id": user_id,
                "english_level": "beginner",
                "conversation_count": 0,
                "created_at": datetime.now()
            }
        
        profile = student_profiles[user_id]
        
        # 特殊命令處理
        if user_message.lower() in ['/start', '開始', 'start']:
            response = f"""
🌟 Welcome to English Learning Bot!

Hi! I'm Emma, your AI English teacher.

Your current level: {profile['english_level'].title()}
Conversations: {profile['conversation_count']}

Let's practice English together! You can:
• Send me any message to start a conversation
• Ask questions about English
• Practice speaking with me

What would you like to talk about today? 😊
            """.strip()
        
        elif user_message.lower() in ['/help', '幫助', 'help']:
            response = """
📚 How to use English Learning Bot:

🗣️ **Text Chat**: Send me messages in English for practice
💬 **Commands**:
   • /start - Begin your learning journey
   • /help - Show this help message

🌟 **Features**:
   • Personalized conversation practice
   • Grammar and vocabulary help
   • Encouraging feedback
   • Progress tracking

Ready to practice? Just start talking! 😊
            """.strip()
        
        else:
            # 使用 AI 回應
            response = await get_ai_response(user_message, profile['english_level'])
            profile['conversation_count'] += 1
        
        # 發送回覆
        try:
            with ApiClient(line_config) as api_client:
                line_bot_api = MessagingApi(api_client)
                line_bot_api.reply_message(
                    ReplyMessageRequest(
                        reply_token=event.reply_token,
                        messages=[TextMessage(text=response)]
                    )
                )
            logger.info("回覆發送成功")
        except Exception as e:
            logger.error(f"發送回覆失敗: {e}")
    
    # 啟動服務
    port = 5002  # 使用不同端口避免衝突
    print("\n啟動 Line Bot 服務")
    print(f"服務地址: http://0.0.0.0:{port}")
    print(f"請先更新 ngrok 轉發到端口 {port}:")
    print(f"   ngrok http {port}")
    print(f"API 文檔: http://localhost:{port}/docs")
    print("\n然後在 Line Developers Console 中使用 /callback 路徑")
    print("按 Ctrl+C 停止服務")

    try:
        uvicorn.run(app, host="0.0.0.0", port=port, log_level="info")
    except KeyboardInterrupt:
        print("\n服務已停止")

if __name__ == "__main__":
    main()
