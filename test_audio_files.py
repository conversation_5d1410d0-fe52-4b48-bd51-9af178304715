#!/usr/bin/env python3
"""
測試語音檔案品質和可播放性
"""

import os
import requests
from datetime import datetime

def check_audio_files():
    """檢查語音檔案"""
    print("🔧 檢查語音檔案")
    print("=" * 50)
    
    audio_dir = "static/audio"
    if not os.path.exists(audio_dir):
        print("❌ 音檔目錄不存在")
        return
    
    files = [f for f in os.listdir(audio_dir) if f.endswith('.mp3')]
    files.sort(key=lambda x: os.path.getctime(os.path.join(audio_dir, x)), reverse=True)
    
    print(f"找到 {len(files)} 個 MP3 檔案")
    
    # 檢查最新的 3 個檔案
    for i, filename in enumerate(files[:3]):
        file_path = os.path.join(audio_dir, filename)
        file_size = os.path.getsize(file_path)
        file_time = datetime.fromtimestamp(os.path.getctime(file_path))
        
        print(f"\n📁 檔案 {i+1}: {filename}")
        print(f"   大小: {file_size} bytes")
        print(f"   建立時間: {file_time}")
        
        # 檢查檔案是否太小
        if file_size < 1000:
            print("   ⚠️  檔案可能太小")
        elif file_size > 1000000:
            print("   ⚠️  檔案可能太大")
        else:
            print("   ✅ 檔案大小正常")

def test_https_access():
    """測試 HTTPS 存取"""
    print("\n🌐 測試 HTTPS 存取")
    print("=" * 50)
    
    base_url = "https://626a-180-177-109-13.ngrok-free.app"
    audio_dir = "static/audio"
    
    files = [f for f in os.listdir(audio_dir) if f.endswith('.mp3')]
    if not files:
        print("❌ 沒有 MP3 檔案可測試")
        return
    
    # 測試最新檔案
    latest_file = max(files, key=lambda x: os.path.getctime(os.path.join(audio_dir, x)))
    test_url = f"{base_url}/audio/{latest_file}"
    
    print(f"測試檔案: {latest_file}")
    print(f"URL: {test_url}")
    
    try:
        response = requests.head(test_url, timeout=10)
        print(f"HTTP 狀態: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type', 'unknown')}")
        print(f"Content-Length: {response.headers.get('content-length', 'unknown')}")
        
        if response.status_code == 200:
            print("✅ HTTPS 存取正常")
        else:
            print("❌ HTTPS 存取失敗")
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

def create_test_audio():
    """創建測試語音檔案"""
    print("\n🎤 創建測試語音檔案")
    print("=" * 50)
    
    try:
        from gtts import gTTS
        import tempfile
        import uuid
        import shutil
        
        # 測試文字
        test_texts = [
            "Hello! This is a test audio file.",
            "Can you hear me clearly?",
            "Testing one two three."
        ]
        
        for i, text in enumerate(test_texts):
            print(f"生成測試檔案 {i+1}: {text}")
            
            try:
                # 生成語音
                tts = gTTS(text=text, lang='en', slow=False)
                
                # 創建臨時檔案
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                temp_file_path = temp_file.name
                temp_file.close()
                
                # 保存語音檔案
                tts.save(temp_file_path)
                
                # 移動到靜態目錄
                file_id = str(uuid.uuid4())
                filename = f"test_{i+1}_{file_id}.mp3"
                static_file_path = os.path.join("static/audio", filename)
                
                shutil.copy2(temp_file_path, static_file_path)
                
                # 檢查檔案
                file_size = os.path.getsize(static_file_path)
                print(f"   ✅ 檔案已生成: {filename} ({file_size} bytes)")
                
                # 清理臨時檔案
                os.unlink(temp_file_path)
                
            except Exception as e:
                print(f"   ❌ 生成失敗: {e}")
                
    except Exception as e:
        print(f"❌ gTTS 不可用: {e}")

def diagnose_audio_issues():
    """診斷語音問題"""
    print("\n🔍 診斷語音問題")
    print("=" * 50)
    
    print("📋 可能的問題：")
    print("1. 檔案格式不相容 - LINE 可能不支援某些 MP3 格式")
    print("2. 檔案大小問題 - 太小或太大的檔案可能無法播放")
    print("3. 編碼問題 - gTTS 生成的編碼可能有問題")
    print("4. URL 存取問題 - ngrok 可能有存取限制")
    print("5. LINE 快取問題 - 相同 URL 可能被快取")
    
    print("\n💡 解決方案：")
    print("1. 測試不同的 gTTS 參數")
    print("2. 檢查檔案是否可以在瀏覽器中播放")
    print("3. 嘗試不同的音檔格式")
    print("4. 檢查 LINE Bot 日誌")
    print("5. 測試較短的語音內容")

def show_line_audio_requirements():
    """顯示 LINE 語音要求"""
    print("\n📱 LINE Bot 語音檔案要求")
    print("=" * 50)
    
    print("📋 LINE Bot 語音訊息規格：")
    print("• 格式: M4A, MP3")
    print("• 最大檔案大小: 10MB")
    print("• 最大時長: 1分鐘")
    print("• URL: 必須是 HTTPS")
    print("• 編碼: AAC, MP3")
    
    print("\n🔧 建議設定：")
    print("• 使用較低的位元率")
    print("• 保持檔案較小")
    print("• 測試不同的編碼參數")
    print("• 確保 URL 可直接存取")

if __name__ == "__main__":
    print("🎤 語音檔案診斷工具")
    print("=" * 60)
    
    check_audio_files()
    test_https_access()
    create_test_audio()
    diagnose_audio_issues()
    show_line_audio_requirements()
    
    print("\n📝 建議測試步驟：")
    print("1. 在瀏覽器中直接開啟語音檔案 URL")
    print("2. 檢查檔案是否可以播放")
    print("3. 嘗試生成更短的測試語音")
    print("4. 檢查 LINE Bot 錯誤日誌")
