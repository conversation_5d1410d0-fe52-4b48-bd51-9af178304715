#!/usr/bin/env python3
"""
執行 AutoGen 多代理協作流程
展示 Agent1、Agent2、Agent3 的協作過程
"""

import asyncio
import os
import sys
import logging
from pathlib import Path

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    """主程式"""
    logger.info("🤖 啟動 AutoGen 多代理協作系統")
    
    # 檢查 OpenAI API Key
    openai_api_key = os.getenv('OPENAI_API_KEY')
    if not openai_api_key:
        logger.error("❌ 請設定 OPENAI_API_KEY 環境變數")
        return
    
    try:
        # 導入協作系統
        from autogen_line_app.autogen_collaboration import AutoGenCollaborationSystem
        
        # 建立協作系統
        collaboration_system = AutoGenCollaborationSystem(openai_api_key)
        logger.info("✅ 協作系統初始化完成")
        
        # 執行協作流程
        logger.info("🔄 開始執行協作流程...")
        result = await collaboration_system.generate_final_application()
        
        # 輸出結果
        print("\n" + "="*80)
        print("🎉 AutoGen 多代理協作結果")
        print("="*80)
        print(result)
        
        # 保存結果
        output_file = "autogen_collaboration_result.md"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(result)
        
        logger.info(f"📄 協作結果已保存到: {output_file}")
        
    except ImportError as e:
        logger.error(f"❌ 導入錯誤: {e}")
        logger.info("請確保已安裝所有依賴: pip install -r requirements.txt")
    except Exception as e:
        logger.error(f"❌ 協作失敗: {e}")
        raise

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("👋 協作已停止")
    except Exception as e:
        logger.error(f"💥 意外錯誤: {e}")
        sys.exit(1)
