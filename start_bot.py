#!/usr/bin/env python3
"""
Emma 英語學習 LINE Bot 啟動腳本
使用重組後的模組結構
"""

import os
import sys
import logging
from datetime import datetime

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_environment():
    """檢查環境設定"""
    print("🔧 檢查環境設定...")
    
    required_vars = [
        "LINE_CHANNEL_ACCESS_TOKEN",
        "LINE_CHANNEL_SECRET", 
        "OPENAI_API_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value[:10]}...")
        else:
            print(f"❌ {var}: 未設定")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n❌ 缺少環境變數: {', '.join(missing_vars)}")
        return False
    
    return True

def setup_module_paths():
    """設定模組路徑"""
    print("📁 設定模組路徑...")
    
    # 添加專案根目錄到 Python 路徑
    project_root = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, project_root)
    
    print(f"✅ 專案根目錄: {project_root}")
    
    # 檢查重要目錄
    important_dirs = [
        "modules",
        "modules/voice_conversation",
        "modules/topic_management", 
        "modules/assessment",
        "modules/audio_processing",
        "static/audio",
        "core"
    ]
    
    for dir_path in important_dirs:
        full_path = os.path.join(project_root, dir_path)
        if os.path.exists(full_path):
            print(f"✅ {dir_path}")
        else:
            print(f"❌ {dir_path} - 目錄不存在")

def import_modules():
    """導入必要模組"""
    print("📦 導入模組...")
    
    try:
        # 基本模組
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ 環境變數載入")
        
        # LINE Bot 模組
        from linebot.v3 import WebhookHandler
        from linebot.v3.messaging import Configuration, ApiClient, MessagingApi
        print("✅ LINE Bot SDK")
        
        # OpenAI 模組
        from openai import OpenAI
        print("✅ OpenAI SDK")
        
        # FastAPI 模組
        from fastapi import FastAPI
        from fastapi.staticfiles import StaticFiles
        print("✅ FastAPI")
        
        # 語音處理模組
        try:
            from modules.voice_conversation.voice_conversation_module import VoiceConversationModule
            from modules.topic_management.topic_management_module import TopicManagementModule
            from modules.audio_processing.static_audio_server import process_voice_with_static_server, init_audio_server
            print("✅ 語音處理模組")
        except ImportError as e:
            print(f"⚠️ 語音處理模組導入失敗: {e}")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False

def create_simple_bot():
    """創建簡化版 LINE Bot"""
    print("🤖 創建簡化版 LINE Bot...")
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        from fastapi import FastAPI, Request, HTTPException
        from fastapi.responses import JSONResponse
        from fastapi.staticfiles import StaticFiles
        from linebot.v3 import WebhookHandler
        from linebot.v3.messaging import Configuration, ApiClient, MessagingApi
        from linebot.v3.webhooks import MessageEvent, TextMessageContent, AudioMessageContent
        from linebot.v3.messaging import ReplyMessageRequest, TextMessage
        from linebot.v3.exceptions import InvalidSignatureError
        from openai import OpenAI
        import uvicorn
        
        # 初始化
        line_config = Configuration(access_token=os.getenv('LINE_CHANNEL_ACCESS_TOKEN'))
        handler = WebhookHandler(os.getenv('LINE_CHANNEL_SECRET'))
        openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        
        # 創建 FastAPI 應用
        app = FastAPI(
            title="Emma English Learning Bot",
            description="重組後的英語學習 LINE Bot",
            version="3.0.0"
        )
        
        # 靜態檔案服務
        os.makedirs("static/audio", exist_ok=True)
        app.mount("/audio", StaticFiles(directory="static/audio"), name="audio")
        
        @app.get("/")
        def root():
            return {
                "message": "Emma English Learning Bot (重組版)",
                "status": "healthy",
                "version": "3.0.0",
                "timestamp": datetime.now().isoformat()
            }
        
        @app.get("/health")
        def health():
            return {"status": "healthy", "modules": "reorganized"}
        
        @app.post("/callback")
        async def callback(request: Request):
            signature = request.headers.get('X-Line-Signature', '')
            body = await request.body()
            body_str = body.decode('utf-8')
            
            try:
                handler.handle(body_str, signature)
                return JSONResponse(content={"status": "ok"})
            except InvalidSignatureError:
                raise HTTPException(status_code=400, detail="Invalid signature")
            except Exception as e:
                logger.error(f"Callback 錯誤: {e}")
                raise HTTPException(status_code=500, detail="Internal server error")
        
        @handler.add(MessageEvent, message=TextMessageContent)
        def handle_text_message(event):
            user_message = event.message.text.strip()
            logger.info(f"收到訊息: {user_message}")
            
            if user_message.lower() in ['/start', 'start']:
                response = """
🌟 Welcome to Emma English Learning Bot! (重組版)

Hi! I'm Emma, your AI English teacher.

🎯 **功能狀態：**
✅ 專案已重組為模組化結構
✅ 基本文字對話功能
🔄 語音功能整合中...

📁 **新的專案結構：**
• modules/assessment/ - 課程導入與分級
• modules/voice_conversation/ - 主題式語音對話
• modules/topic_management/ - 主題管理
• modules/audio_processing/ - 音檔處理

💡 發送任何英語訊息開始對話！
                """.strip()
            else:
                # 簡單的 AI 回應
                try:
                    ai_response = openai_client.chat.completions.create(
                        model="gpt-3.5-turbo",
                        messages=[
                            {"role": "system", "content": "你是 Emma，一位友善的英語老師。用英語回應學生，保持鼓勵和支持。"},
                            {"role": "user", "content": user_message}
                        ],
                        max_tokens=200,
                        temperature=0.7
                    )
                    response = ai_response.choices[0].message.content.strip()
                except Exception as e:
                    logger.error(f"AI 回應失敗: {e}")
                    response = "Hello! I'm having some technical difficulties. Let's try again! 😊"
            
            # 發送回覆
            try:
                with ApiClient(line_config) as api_client:
                    line_bot_api = MessagingApi(api_client)
                    line_bot_api.reply_message(
                        ReplyMessageRequest(
                            reply_token=event.reply_token,
                            messages=[TextMessage(text=response)]
                        )
                    )
                logger.info("✅ 回覆發送成功")
            except Exception as e:
                logger.error(f"❌ 發送回覆失敗: {e}")
        
        @handler.add(MessageEvent, message=AudioMessageContent)
        def handle_audio_message(event):
            logger.info("🎤 收到語音訊息")
            
            response = """
🎤 **語音功能狀態**

✅ 語音訊息接收正常
🔄 語音處理模組整合中...

📋 **重組進度：**
• 檔案結構已重組 ✅
• 模組化導入調整中 🔄
• 語音功能恢復中 🔄

💡 **暫時建議：**
請使用文字訊息進行對話
語音功能將很快恢復！
            """.strip()
            
            try:
                with ApiClient(line_config) as api_client:
                    line_bot_api = MessagingApi(api_client)
                    line_bot_api.reply_message(
                        ReplyMessageRequest(
                            reply_token=event.reply_token,
                            messages=[TextMessage(text=response)]
                        )
                    )
                logger.info("✅ 語音狀態回覆發送成功")
            except Exception as e:
                logger.error(f"❌ 發送語音狀態回覆失敗: {e}")
        
        print("✅ 簡化版 LINE Bot 創建成功")
        
        # 啟動服務 - 使用不同端口避免衝突
        port = 5003  # 改用 5003 端口
        print(f"\n🚀 啟動 Emma 英語學習 LINE Bot (端口: {port})...")
        print("=" * 50)
        print(f"💡 如果端口 {port} 被占用，請手動修改 start_bot.py 中的端口號")
        uvicorn.run(app, host="0.0.0.0", port=port, log_level="info")
        
    except Exception as e:
        print(f"❌ 創建 LINE Bot 失敗: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函數"""
    print("🎤 Emma 英語學習 LINE Bot 啟動器")
    print("=" * 60)
    
    # 檢查環境
    if not check_environment():
        print("\n❌ 環境檢查失敗，請設定必要的環境變數")
        return
    
    # 設定路徑
    setup_module_paths()
    
    # 嘗試導入模組
    if import_modules():
        print("\n✅ 所有模組導入成功，啟動完整版...")
        # 這裡可以啟動完整版
        create_simple_bot()
    else:
        print("\n⚠️ 模組導入失敗，啟動簡化版...")
        create_simple_bot()

if __name__ == "__main__":
    main()
