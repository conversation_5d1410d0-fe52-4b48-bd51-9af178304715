#!/usr/bin/env python3
"""
gTTS 語音回覆模組
使用 Google Text-to-Speech 生成語音回覆

功能：
1. 文字轉語音 (gTTS)
2. 語音檔案管理
3. LINE Bot 語音訊息發送
"""

import os
import logging
import tempfile
import requests
from typing import Optional
from gtts import gTTS
import io

# 設定日誌
logger = logging.getLogger(__name__)

class GTTSVoiceModule:
    """gTTS 語音回覆模組"""
    
    def __init__(self, line_bot_api=None):
        """初始化 gTTS 語音模組"""
        self.line_bot_api = line_bot_api
        self.temp_files = []  # 追蹤臨時檔案以便清理
    
    def text_to_speech(self, text: str, lang: str = 'en', slow: bool = False) -> Optional[str]:
        """
        將文字轉換為語音檔案
        
        Args:
            text: 要轉換的文字
            lang: 語言代碼 ('en' 為英語, 'zh' 為中文)
            slow: 是否使用慢速語音
            
        Returns:
            語音檔案路徑，失敗則返回 None
        """
        try:
            # 創建 gTTS 物件
            tts = gTTS(text=text, lang=lang, slow=slow)
            
            # 創建臨時檔案
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
            temp_file_path = temp_file.name
            temp_file.close()
            
            # 保存語音檔案
            tts.save(temp_file_path)
            
            # 記錄臨時檔案以便後續清理
            self.temp_files.append(temp_file_path)
            
            logger.info(f"語音檔案生成成功: {temp_file_path}")
            return temp_file_path
            
        except Exception as e:
            logger.error(f"gTTS 語音生成失敗: {e}")
            return None
    
    def create_teacher_voice_response(self, teacher_text: str) -> Optional[str]:
        """
        為英語老師的回應創建語音檔案
        
        Args:
            teacher_text: 老師的英文回應
            
        Returns:
            語音檔案路徑
        """
        try:
            # 使用英語，稍微慢一點的語速適合學習
            return self.text_to_speech(teacher_text, lang='en', slow=True)
        except Exception as e:
            logger.error(f"老師語音生成失敗: {e}")
            return None
    
    def upload_audio_to_line(self, audio_file_path: str) -> Optional[str]:
        """
        將語音檔案上傳到可公開存取的位置
        
        注意：這是一個示例實現，實際使用時需要：
        1. 上傳到雲端存儲 (如 AWS S3, Google Cloud Storage)
        2. 或使用 ngrok 等工具暴露本地檔案
        3. 確保 LINE Bot 可以存取該 URL
        
        Args:
            audio_file_path: 本地語音檔案路徑
            
        Returns:
            可公開存取的 URL
        """
        try:
            # 這裡需要實現檔案上傳邏輯
            # 暫時返回本地檔案路徑（實際使用時需要替換）
            
            # 示例：如果您有 ngrok 或其他公開 URL
            # 假設您的服務運行在 localhost:5002
            # 並且有一個靜態檔案服務端點
            
            filename = os.path.basename(audio_file_path)
            # 這需要您設置一個靜態檔案服務
            public_url = f"https://your-domain.com/audio/{filename}"
            
            logger.info(f"語音檔案 URL: {public_url}")
            return public_url
            
        except Exception as e:
            logger.error(f"語音檔案上傳失敗: {e}")
            return None
    
    def cleanup_temp_files(self):
        """清理臨時檔案"""
        for file_path in self.temp_files:
            try:
                if os.path.exists(file_path):
                    os.unlink(file_path)
                    logger.info(f"清理臨時檔案: {file_path}")
            except Exception as e:
                logger.error(f"清理檔案失敗 {file_path}: {e}")
        
        self.temp_files.clear()

class EnhancedVoiceFeedbackWithGTTS:
    """增強版語音回饋（使用 gTTS）"""
    
    def __init__(self, openai_client, line_bot_api=None):
        """初始化增強版語音回饋"""
        self.openai_client = openai_client
        self.line_bot_api = line_bot_api
        self.gtts_module = GTTSVoiceModule(line_bot_api)
    
    def generate_voice_feedback(self, transcribed_text: str, topic: str, user_level: str) -> tuple:
        """
        生成包含語音的完整回饋
        
        Returns:
            (feedback_text, voice_file_path)
        """
        try:
            # 生成中文翻譯
            translation = self._get_translation(transcribed_text)
            
            # 生成英文老師的回應
            teacher_response = self._get_teacher_response(transcribed_text, topic, user_level)
            
            # 生成老師回應的語音檔案
            voice_file = self.gtts_module.create_teacher_voice_response(teacher_response)
            
            # 生成學習建議
            improvement = self._get_improvement_suggestion(transcribed_text, topic, user_level)
            
            # 格式化回饋訊息
            feedback_message = f"""
🎤 **您說的話：**
"{transcribed_text}"

📝 **中文翻譯：**
{translation}

👩‍🏫 **Emma 老師回應：**
{teacher_response}

🔊 **語音回覆：**
{"✅ 語音檔案已生成！" if voice_file else "❌ 語音生成失敗"}

✨ **學習建議：**
{improvement}

---

🎯 **繼續對話：**
• 聽取老師的語音回覆
• 回應老師的問題
• 練習相關詞彙

💪 Keep practicing! You're doing great! 😊
            """.strip()
            
            return feedback_message, voice_file
            
        except Exception as e:
            logger.error(f"生成語音回饋失敗: {e}")
            return self._get_fallback_feedback(transcribed_text), None
    
    def _get_translation(self, text: str) -> str:
        """獲取中文翻譯"""
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "請將英文翻譯成自然的中文。"},
                    {"role": "user", "content": f"翻譯：{text}"}
                ],
                max_tokens=150,
                temperature=0.1
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"翻譯失敗: {e}")
            return "翻譯暫時無法使用，但您的英語表達很不錯！"
    
    def _get_teacher_response(self, text: str, topic: str, level: str) -> str:
        """生成英文老師的回應"""
        try:
            prompt = f"""
            你是 Emma，一位友善的英語老師。學生剛剛對你說："{text}"
            
            當前主題是：{topic}
            學生程度：{level}
            
            請像真正的英語老師一樣回應學生：
            1. 直接回答學生的問題（如果有問題的話）
            2. 針對主題給出相關的回應
            3. 鼓勵學生繼續對話
            4. 可以問一個相關的問題來延續對話
            
            請用自然的英語回應，控制在 2-3 句話內，適合語音播放。
            """
            
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "你是 Emma，一位專業且友善的英語老師，擅長與學生進行自然對話。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=200,
                temperature=0.7
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"老師回應生成失敗: {e}")
            return "That's great! I'm here to help you practice English. What would you like to talk about?"
    
    def _get_improvement_suggestion(self, text: str, topic: str, level: str) -> str:
        """獲取改進建議"""
        try:
            prompt = f"""
            學習者說了："{text}"
            主題是：{topic}
            程度：{level}
            
            請給出簡短的學習建議（1-2句話），包括：
            1. 鼓勵的話
            2. 一個具體的改進建議
            
            請用友善鼓勵的語氣。
            """
            
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "你是友善的英語老師，給出簡短有用的建議。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=150,
                temperature=0.5
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"建議生成失敗: {e}")
            return "很好的嘗試！繼續練習會讓您的英語更流利。"
    
    def _get_fallback_feedback(self, text: str) -> str:
        """備用回饋訊息"""
        return f"""
🎤 **您說的話：**
"{text}"

👩‍🏫 **Emma 老師回應：**
That's wonderful! Thank you for practicing with me. Keep up the great work!

📝 **回饋：**
很棒的語音練習！您的發音很清楚。

✨ **建議：**
• 繼續保持練習的習慣
• 嘗試使用更多詞彙
• 表達更完整的想法

💪 繼續加油，您做得很好！
        """.strip()
    
    def cleanup(self):
        """清理資源"""
        self.gtts_module.cleanup_temp_files()

# 整合函數
def process_voice_with_gtts(openai_client, transcribed_text: str, topic: str, user_level: str, line_bot_api=None) -> tuple:
    """
    使用 gTTS 的語音處理函數
    
    Returns:
        (feedback_text, voice_file_path)
    """
    try:
        feedback_processor = EnhancedVoiceFeedbackWithGTTS(openai_client, line_bot_api)
        result = feedback_processor.generate_voice_feedback(transcribed_text, topic, user_level)
        return result
    except Exception as e:
        logger.error(f"gTTS 語音處理失敗: {e}")
        fallback_text = f"""
🎤 **您說的話：**
"{transcribed_text}"

👩‍🏫 **Emma 老師回應：**
Thank you for practicing with me! That was great!

🔊 **語音狀態：**
語音功能暫時無法使用，但您的練習很棒！

💪 繼續加油！ 😊
        """.strip()
        return fallback_text, None
