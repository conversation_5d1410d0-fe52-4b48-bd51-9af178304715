#!/usr/bin/env python3
"""
測試增強版語音回饋功能
"""

import os
from dotenv import load_dotenv
from openai import OpenAI
from voice_response_module import process_voice_enhanced

# 載入環境變數
load_dotenv()

def test_enhanced_voice_feedback():
    """測試增強版語音回饋功能"""
    
    # 初始化 OpenAI 客戶端
    openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    
    # 測試案例 - 模擬您的語音輸入
    test_cases = [
        {
            "text": "What is the topic today?",
            "topic": "旅遊經驗",
            "level": "intermediate",
            "description": "學生詢問今日主題"
        },
        {
            "text": "I like to travel to different countries",
            "topic": "旅遊經驗", 
            "level": "intermediate",
            "description": "學生分享旅遊喜好"
        },
        {
            "text": "Can you help me practice English?",
            "topic": "自我介紹",
            "level": "beginner",
            "description": "學生請求練習幫助"
        }
    ]
    
    print("🧪 測試增強版語音回饋功能")
    print("=" * 60)
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📝 測試案例 {i}: {case['description']}")
        print(f"學生說: \"{case['text']}\"")
        print(f"主題: {case['topic']}")
        print(f"程度: {case['level']}")
        print("-" * 40)
        
        try:
            feedback = process_voice_enhanced(
                openai_client, 
                case['text'], 
                case['topic'], 
                case['level']
            )
            print("✅ Emma 老師的完整回應:")
            print(feedback)
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
        
        print("\n" + "=" * 60)

if __name__ == "__main__":
    test_enhanced_voice_feedback()
    
    print("\n🎉 測試完成！")
    print("\n📱 現在的語音功能包含：")
    print("• 🎤 語音轉文字顯示")
    print("• 📝 中文翻譯")
    print("• 👩‍🏫 Emma 老師的英文回應（回答您的問題）")
    print("• 🔊 語音回覆說明（未來會加入真正的語音）")
    print("• ✨ 個人化學習建議")
    print("• 💪 鼓勵和練習指導")
    print("\n現在 Emma 老師會像真正的老師一樣回應您的問題了！ 😊")
