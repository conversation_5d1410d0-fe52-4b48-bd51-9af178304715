# 應用程式設定
APP_NAME=My Line English Bot API
APP_VERSION=1.0.0
DEBUG=true

# 伺服器設定
HOST=0.0.0.0
PORT=8000

# API 設定
API_PREFIX=/api/v1
DOCS_URL=/docs
REDOC_URL=/redoc

# CORS 設定
ALLOWED_ORIGINS=["*"]
ALLOWED_METHODS=["*"]
ALLOWED_HEADERS=["*"]

# 資料庫設定
DATABASE_URL=postgresql://user:password@localhost/dbname

# Line Bot 設定
LINE_CHANNEL_ACCESS_TOKEN=your_line_channel_access_token_here
LINE_CHANNEL_SECRET=your_line_channel_secret_here

# OpenAI 設定
OPENAI_API_KEY=your_openai_api_key_here

# AutoGen Line Bot 特定設定
AUTOGEN_MODEL=gpt-4o
AUTOGEN_TEMPERATURE=0.7
AUTOGEN_MAX_TOKENS=2000

# 語音處理設定
SPEECH_RECOGNITION_LANGUAGE=en-US
AZURE_SPEECH_KEY=your_azure_speech_key_here
AZURE_SPEECH_REGION=eastus

# 學習功能設定
DEFAULT_ENGLISH_LEVEL=beginner
MAX_CONVERSATION_HISTORY=50
VOCABULARY_LEARNING_THRESHOLD=5

# 日誌設定
LOG_LEVEL=INFO
