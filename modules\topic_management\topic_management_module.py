#!/usr/bin/env python3
"""
主題管理模組
管理每日主題推送、主題分類、難度適配

功能：
1. 每日主題推送
2. 主題分類管理
3. 難度適配
4. 學習進度追蹤
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import random

# 設定日誌
logger = logging.getLogger(__name__)

class TopicCategory(Enum):
    """主題分類"""
    DAILY_LIFE = "日常生活"
    WORK_BUSINESS = "工作商務"
    TRAVEL_CULTURE = "旅遊文化"
    TECHNOLOGY = "科技"
    HEALTH_LIFESTYLE = "健康生活"
    ENTERTAINMENT = "娛樂"
    EDUCATION = "教育"
    SOCIAL_ISSUES = "社會議題"

class DifficultyLevel(Enum):
    """難度等級"""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"

@dataclass
class TopicData:
    """主題數據"""
    id: str
    name: str
    category: TopicCategory
    difficulty: DifficultyLevel
    description: str
    prompt: str
    questions: List[str]
    vocabulary: List[str]
    learning_objectives: List[str]

@dataclass
class UserTopicProgress:
    """用戶主題進度"""
    user_id: str
    topic_id: str
    started_at: datetime
    completed_at: Optional[datetime]
    conversation_count: int
    mastery_score: float

class TopicManagementModule:
    """主題管理模組"""
    
    def __init__(self):
        """初始化主題管理模組"""
        self.topics_database = self._load_topics_database()
        self.user_progress = {}  # 用戶進度追蹤
        self.daily_topic_history = {}  # 每日主題歷史
    
    def _load_topics_database(self) -> Dict[str, TopicData]:
        """載入主題資料庫"""
        topics = {}
        
        # 初級主題
        beginner_topics = [
            {
                "id": "intro_001",
                "name": "自我介紹",
                "category": TopicCategory.DAILY_LIFE,
                "difficulty": DifficultyLevel.BEGINNER,
                "description": "學習如何用英語介紹自己",
                "prompt": "今天我們來練習自我介紹！請用英語介紹你自己，包括姓名、年齡、興趣等。",
                "questions": [
                    "What's your name?",
                    "How old are you?",
                    "What do you like to do in your free time?",
                    "Where are you from?"
                ],
                "vocabulary": ["name", "age", "hobby", "like", "enjoy", "from", "live", "work"],
                "learning_objectives": ["基本個人資訊表達", "興趣愛好描述", "簡單問答互動"]
            },
            {
                "id": "food_001",
                "name": "美食與用餐",
                "category": TopicCategory.DAILY_LIFE,
                "difficulty": DifficultyLevel.BEGINNER,
                "description": "學習描述食物和用餐經驗",
                "prompt": "讓我們聊聊美食！請分享你最喜歡的食物或最近的用餐經驗。",
                "questions": [
                    "What's your favorite food?",
                    "Do you like cooking?",
                    "What did you eat for breakfast today?"
                ],
                "vocabulary": ["food", "delicious", "cook", "restaurant", "breakfast", "lunch", "dinner"],
                "learning_objectives": ["食物詞彙", "喜好表達", "用餐經驗分享"]
            },
            {
                "id": "shopping_001",
                "name": "購物體驗",
                "category": TopicCategory.DAILY_LIFE,
                "difficulty": DifficultyLevel.BEGINNER,
                "description": "學習購物相關的英語表達",
                "prompt": "今天我們談談購物！請分享你的購物習慣或最近買的東西。",
                "questions": [
                    "Do you like shopping?",
                    "What did you buy recently?",
                    "Where do you usually go shopping?"
                ],
                "vocabulary": ["buy", "shop", "store", "price", "expensive", "cheap", "money"],
                "learning_objectives": ["購物詞彙", "價格討論", "購物場所描述"]
            }
        ]
        
        # 中級主題
        intermediate_topics = [
            {
                "id": "work_001",
                "name": "職場生活",
                "category": TopicCategory.WORK_BUSINESS,
                "difficulty": DifficultyLevel.INTERMEDIATE,
                "description": "討論工作經驗和職業規劃",
                "prompt": "今天我們討論職場話題！請分享你的工作經驗或職業規劃。",
                "questions": [
                    "What kind of work do you do?",
                    "What are your career goals?",
                    "How do you handle workplace challenges?"
                ],
                "vocabulary": ["career", "professional", "colleague", "responsibility", "achievement", "challenge"],
                "learning_objectives": ["職業描述", "目標規劃", "問題解決表達"]
            },
            {
                "id": "travel_001",
                "name": "旅遊經驗",
                "category": TopicCategory.TRAVEL_CULTURE,
                "difficulty": DifficultyLevel.INTERMEDIATE,
                "description": "分享旅遊經驗和文化體驗",
                "prompt": "讓我們聊聊旅遊！請分享你最難忘的旅行經驗或夢想中的目的地。",
                "questions": [
                    "What's the most interesting place you've visited?",
                    "How do you usually plan your trips?",
                    "What cultural differences have you noticed while traveling?"
                ],
                "vocabulary": ["destination", "culture", "experience", "adventure", "explore", "memorable"],
                "learning_objectives": ["經驗敘述", "文化比較", "計劃表達"]
            }
        ]
        
        # 高級主題
        advanced_topics = [
            {
                "id": "tech_001",
                "name": "科技與社會",
                "category": TopicCategory.TECHNOLOGY,
                "difficulty": DifficultyLevel.ADVANCED,
                "description": "深入討論科技對社會的影響",
                "prompt": "讓我們深入討論科技話題！你認為人工智能和數位化如何改變我們的社會？",
                "questions": [
                    "How has artificial intelligence impacted various industries?",
                    "What are the ethical implications of advanced technology?",
                    "How can we balance technological progress with privacy concerns?"
                ],
                "vocabulary": ["artificial intelligence", "digitalization", "innovation", "ethics", "privacy", "automation"],
                "learning_objectives": ["複雜議題分析", "觀點論述", "批判性思考表達"]
            },
            {
                "id": "env_001",
                "name": "環境與永續發展",
                "category": TopicCategory.SOCIAL_ISSUES,
                "difficulty": DifficultyLevel.ADVANCED,
                "description": "討論環境保護和永續發展議題",
                "prompt": "今天我們討論環境議題！請分享你對氣候變遷和永續發展的看法。",
                "questions": [
                    "What are the most pressing environmental challenges we face?",
                    "How can individuals contribute to sustainable development?",
                    "What role should corporations play in environmental protection?"
                ],
                "vocabulary": ["sustainability", "climate change", "renewable energy", "carbon footprint", "biodiversity"],
                "learning_objectives": ["環境議題討論", "解決方案提出", "責任分析"]
            }
        ]
        
        # 將所有主題加入資料庫
        all_topics = beginner_topics + intermediate_topics + advanced_topics
        
        for topic_data in all_topics:
            topic = TopicData(
                id=topic_data["id"],
                name=topic_data["name"],
                category=topic_data["category"],
                difficulty=topic_data["difficulty"],
                description=topic_data["description"],
                prompt=topic_data["prompt"],
                questions=topic_data["questions"],
                vocabulary=topic_data["vocabulary"],
                learning_objectives=topic_data["learning_objectives"]
            )
            topics[topic.id] = topic
        
        return topics
    
    def get_daily_topic(self, user_id: str, user_level: str) -> TopicData:
        """獲取每日主題"""
        today = datetime.now().date()
        
        # 檢查今天是否已經推送過主題
        if user_id in self.daily_topic_history:
            user_history = self.daily_topic_history[user_id]
            if today in user_history:
                topic_id = user_history[today]
                return self.topics_database[topic_id]
        
        # 根據用戶程度篩選適合的主題
        suitable_topics = [
            topic for topic in self.topics_database.values()
            if topic.difficulty.value == user_level
        ]
        
        # 避免重複推送最近的主題
        recent_topics = self._get_recent_topics(user_id, days=7)
        available_topics = [
            topic for topic in suitable_topics
            if topic.id not in recent_topics
        ]
        
        # 如果沒有可用主題，重置歷史
        if not available_topics:
            available_topics = suitable_topics
        
        # 隨機選擇一個主題
        selected_topic = random.choice(available_topics)
        
        # 記錄今日主題
        if user_id not in self.daily_topic_history:
            self.daily_topic_history[user_id] = {}
        self.daily_topic_history[user_id][today] = selected_topic.id
        
        return selected_topic
    
    def _get_recent_topics(self, user_id: str, days: int = 7) -> List[str]:
        """獲取最近幾天的主題ID"""
        if user_id not in self.daily_topic_history:
            return []
        
        recent_topics = []
        today = datetime.now().date()
        
        for i in range(days):
            date = today - timedelta(days=i)
            if date in self.daily_topic_history[user_id]:
                recent_topics.append(self.daily_topic_history[user_id][date])
        
        return recent_topics
    
    def get_topics_by_category(self, category: TopicCategory, difficulty: DifficultyLevel) -> List[TopicData]:
        """根據分類和難度獲取主題"""
        return [
            topic for topic in self.topics_database.values()
            if topic.category == category and topic.difficulty == difficulty
        ]
    
    def get_topic_by_id(self, topic_id: str) -> Optional[TopicData]:
        """根據ID獲取主題"""
        return self.topics_database.get(topic_id)
    
    def format_topic_message(self, topic: TopicData) -> str:
        """格式化主題訊息"""
        message = f"""
🎯 **今日英語對話主題**

📚 **主題：{topic.name}**
🎓 **難度：{topic.difficulty.value.title()}**
📂 **分類：{topic.category.value}**

💡 **主題介紹：**
{topic.description}

🗣️ **對話引導：**
{topic.prompt}

❓ **參考問題：**
        """.strip()
        
        for i, question in enumerate(topic.questions, 1):
            message += f"\n{i}. {question}"
        
        message += f"""

📝 **重點詞彙：**
{', '.join(topic.vocabulary)}

🎯 **學習目標：**
        """
        
        for objective in topic.learning_objectives:
            message += f"\n• {objective}"
        
        message += """

🎤 **開始練習：**
請錄製語音訊息回應這個主題，我會為您提供即時回饋和建議！
        """
        
        return message
    
    def update_user_progress(self, user_id: str, topic_id: str, conversation_count: int = 1):
        """更新用戶學習進度"""
        if user_id not in self.user_progress:
            self.user_progress[user_id] = {}
        
        if topic_id not in self.user_progress[user_id]:
            self.user_progress[user_id][topic_id] = UserTopicProgress(
                user_id=user_id,
                topic_id=topic_id,
                started_at=datetime.now(),
                completed_at=None,
                conversation_count=0,
                mastery_score=0.0
            )
        
        progress = self.user_progress[user_id][topic_id]
        progress.conversation_count += conversation_count
        
        # 簡單的熟練度計算（可以後續優化）
        progress.mastery_score = min(progress.conversation_count * 20, 100)
        
        # 如果達到一定熟練度，標記為完成
        if progress.mastery_score >= 80 and progress.completed_at is None:
            progress.completed_at = datetime.now()
    
    def get_user_progress_summary(self, user_id: str) -> Dict:
        """獲取用戶學習進度摘要"""
        if user_id not in self.user_progress:
            return {"total_topics": 0, "completed_topics": 0, "in_progress": 0}
        
        user_topics = self.user_progress[user_id]
        completed = sum(1 for p in user_topics.values() if p.completed_at is not None)
        in_progress = len(user_topics) - completed
        
        return {
            "total_topics": len(user_topics),
            "completed_topics": completed,
            "in_progress": in_progress,
            "topics_detail": user_topics
        }
