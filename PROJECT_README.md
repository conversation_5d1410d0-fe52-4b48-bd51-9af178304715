# mylineenglishbot - Emma 英語學習 LINE Bot

🎤 一個功能完整的 AI 英語學習 LINE Bot，具備語音對話、程度評估、主題式學習等功能。

## 📁 專案結構

```
mylineenglishbot/
├── main.py                    # 主要入口點
├── core/                      # 核心功能
│   └── line_bot_autogen_fixed.py
├── modules/                   # 功能模組
│   ├── assessment/           # 課程導入與分級
│   │   ├── level_assessment_module.py
│   │   ├── assessment_integration.py
│   │   └── autogen_level_assessment_system.py
│   ├── voice_conversation/   # 主題式語音對話
│   │   ├── voice_conversation_module.py
│   │   ├── voice_feedback_simple.py
│   │   ├── voice_response_module.py
│   │   └── autogen_voice_conversation_system.py
│   ├── topic_management/     # 主題管理
│   │   └── topic_management_module.py
│   └── audio_processing/     # 音檔處理
│       ├── gtts_voice_module.py
│       └── static_audio_server.py
├── config/                   # 配置檔案
│   ├── LineBotChannelKeys.txt
│   ├── https_config.txt
│   └── requirements.txt
├── static/                   # 靜態檔案
│   └── audio/               # 語音檔案
├── scripts/                 # 工具腳本
├── docs/                    # 文檔
├── temp/                    # 測試檔案
└── __pycache__/            # Python 快取
```

## 🚀 快速開始

1. 安裝依賴：
   ```bash
   pip install -r config/requirements.txt
   ```

2. 設定環境變數：
   - 複製 `config/LineBotChannelKeys.txt` 並填入您的 LINE Bot 金鑰
   - 設定 OpenAI API 金鑰

3. 啟動服務：
   ```bash
   python main.py
   ```

4. 設定 ngrok (用於 HTTPS)：
   ```bash
   ngrok http 5002
   ```

## 🎯 主要功能

### 1. 課程導入與分級 (modules/assessment/)
- 英語程度評估測驗
- 自動分級系統
- 個人化學習路徑

### 2. 主題式語音對話 (modules/voice_conversation/)
- Whisper API 語音轉文字
- GPT AI 對話生成
- gTTS 語音回覆
- 即時語音回饋

### 3. 主題管理 (modules/topic_management/)
- 每日學習主題
- 進度追蹤
- 個人化推薦

### 4. 音檔處理 (modules/audio_processing/)
- 語音合成 (gTTS)
- 靜態檔案服務
- HTTPS 音檔存取

## 🔧 技術架構

- **LINE Bot SDK v3** - LINE 訊息處理
- **OpenAI API** - Whisper 語音轉文字 + GPT 對話
- **gTTS** - Google Text-to-Speech 語音合成
- **FastAPI** - Web 服務框架
- **ngrok** - HTTPS 隧道服務

## 👩‍🏫 Emma 老師功能

Emma 是您的 AI 英語老師，具備：
- 🎤 語音對話能力
- 📝 即時回饋與建議
- 🎯 主題式教學
- 📊 學習進度追蹤
- 🔊 標準英語發音

## 📱 使用方式

1. 加入 LINE Bot 好友
2. 發送 `/start` 開始使用
3. 完成英語程度評估
4. 開始語音對話練習
5. 接收個人化學習建議

---

**讓 Emma 老師陪您一起學英語！** 🎤👩‍🏫✨
