@echo off
chcp 65001 >nul
echo ========================================
echo AutoGen Line Bot Install and Run Script
echo ========================================

echo.
echo 1. Checking Python version...
python --version
if %errorlevel% neq 0 (
    echo Error: Python not installed or not in PATH
    pause
    exit /b 1
)

echo.
echo 2. Upgrading pip...
python -m pip install --upgrade pip

echo.
echo 3. Installing basic dependencies...
pip install fastapi==0.104.1
pip install "uvicorn[standard]==0.24.0"
pip install line-bot-sdk==3.7.0
pip install openai
pip install requests
pip install pydantic==2.5.0

echo.
echo 4. Checking environment variables...
if "%LINE_CHANNEL_ACCESS_TOKEN%"=="" (
    echo Warning: LINE_CHANNEL_ACCESS_TOKEN not set
    echo Please set environment variables or edit .env file
)

if "%LINE_CHANNEL_SECRET%"=="" (
    echo Warning: LINE_CHANNEL_SECRET not set
    echo Please set environment variables or edit .env file
)

if "%OPENAI_API_KEY%"=="" (
    echo Warning: OPENAI_API_KEY not set
    echo Please set environment variables or edit .env file
)

echo.
echo 5. Starting Line Bot...
python run_simple_line_bot.py

pause
