from flask import Flask, request, abort
from linebot.v3 import <PERSON><PERSON>ok<PERSON>and<PERSON>
from linebot.v3.exceptions import InvalidSignatureError
from linebot.v3.messaging import Configuration, ApiClient, MessagingApi, ReplyMessageRequest, TextMessage
from linebot.v3.webhooks import MessageEvent, TextMessageContent
from linebot.v3.messaging import <PERSON>R<PERSON>ly, QuickReplyItem, MessageAction, TextMessage
from linebot.v3.messaging import Configuration, ApiClient, MessagingApi, TextMessage, PushMessageRequest
configuration = Configuration(access_token='7FlCIuCugL9uz1zuloZSuioLdOfprxgoK3+1Orm+9ZrFDxU2rNJLKunDeXZ5J/tWPFJVoxExA473G64TaEEveUVkIMsbjeALhuIcdsv0QDUldmFYMBUXm5it6xlv0rHm6lGkrmkFnRNMyexPLQXVHAdB04t89/1O/w1cDnyilFU=')
messaging_api = MessagingApi(configuration)
from linebot.v3.messaging import PushMessageRequest

from apscheduler.schedulers.background import BackgroundScheduler
from datetime import datetime

import openai
openai.api_key = '********************************************************************************************************************************************************************'
#----------------------------------------------------------------------------------------------------------


def get_ai_topic():
    prompt = "Please suggest a practical, interesting English conversation topic for today, suitable for daily life practice."
    response = openai.ChatCompletion.create(
        model="gpt-4",
        messages=[{"role": "user", "content": prompt}],
        max_tokens=50,
        temperature=0.7
    )
    topic = response['choices']['message']['content'].strip()
    return topic


###時間設定的程式碼
#----------------------------------------------------------------------------------------------------------
#2.學員時間設定資料結構（範例）
# 假設這是從資料庫取得的學員推播時間設定
user_push_times = {
    'user_id_1': '08:30',
    'user_id_2': '19:00',
    # ...更多學員
}
#3.設定排程與推播訊息

#學員推播時間設定
user_push_times = {
    'user_id_1': '08:30',
    'user_id_2': '19:00',
}
        
def send_daily_message(user_id):
    message = TextMessage(text="Good morning! Here's an interesting topic for you today!")
    messaging_api.push_message(
        PushMessageRequest(
            to=user_id,
            messages=[message]
        )
    )        
        
        
        
        
        
#----------------------------------------------------------------------------------------------------------



# 建立老師類別
class Teacher:
    def __init__(self, id, name, gender, personality=None, interests=None, english_level=None):
        self.id = id
        self.name = name
        self.gender = gender
        self.personality = personality
        self.interests = interests
        self.english_level = english_level
        
def show_teacher_menu():
    menu = []
    for teacher in teacher_menu:
        menu.append({'id': teacher.id, 'name': teacher.name, 'gender': teacher.gender})
    return menu
    
def get_teacher_quick_reply():
    items = []
    for teacher in teacher_menu:
        items.append(
            QuickReplyItem(
                action=MessageAction(
                    label=f"{teacher.name} ({teacher.gender})",
                    text=f"選擇老師:{teacher.id}"
                )
            )
        )
    return QuickReply(items=items)

# 執行結果
# [{'id': 1, 'name': 'John', 'gender': 'male'}, 
#  {'id': 2, 'name': 'Emma', 'gender': 'female'}]
        

# 建立兩位老師
teacher1 = Teacher(id=1, name='John', gender='male', personality='friendly', interests=['sports', 'technology'], english_level='intermediate')
teacher2 = Teacher(id=2, name='Emma', gender='female', personality='patient', interests=['music', 'travel'], english_level='beginner')

teacher_menu = [teacher1, teacher2]



#----------------------------------------------------------------------------------------------------------
app = Flask(__name__)
configuration = Configuration(access_token='7FlCIuCugL9uz1zuloZSuioLdOfprxgoK3+1Orm+9ZrFDxU2rNJLKunDeXZ5J/tWPFJVoxExA473G64TaEEveUVkIMsbjeALhuIcdsv0QDUldmFYMBUXm5it6xlv0rHm6lGkrmkFnRNMyexPLQXVHAdB04t89/1O/w1cDnyilFU=')
handler = WebhookHandler('910dbb524417055ebadbd276bf9474c8')

@app.route("/callback", methods=['POST'])
def callback():
    signature = request.headers['X-Line-Signature']
    body = request.get_data(as_text=True)
    try:
        handler.handle(body, signature)
    except InvalidSignatureError:
        abort(400)
    return 'OK'

###開場白
@handler.add(MessageEvent, message=TextMessageContent)
def handle_message(event):
    with ApiClient(configuration) as api_client:
        line_bot_api = MessagingApi(api_client)
        # 這裡可以串接 OpenAI 回應
        line_bot_api.reply_message_with_http_info(
            ReplyMessageRequest(
                reply_token=event.reply_token,
                messages=[TextMessage(text="Hello! 這是英文口說學習機器人")]
            )
        )
###選擇老師功能
@handler.add(MessageEvent, message=TextMessageContent)
def handle_message(event):
    user_message = event.message.text.strip()
    with ApiClient(configuration) as api_client:
        line_bot_api = MessagingApi(api_client)
        if user_message == "選擇老師":
            line_bot_api.reply_message_with_http_info(
                ReplyMessageRequest(
                    reply_token=event.reply_token,
                    messages=[
                        TextMessage(
                            text="請選擇一位老師：",
                            quick_reply=get_teacher_quick_reply()
                        )
                    ]
                )
            )
        elif user_message.startswith("選擇老師:"):
            teacher_id = int(user_message.split(":")[1])
            # 儲存用戶選擇的老師，可以用 session、資料庫或 dict
            line_bot_api.reply_message_with_http_info(
                ReplyMessageRequest(
                    reply_token=event.reply_token,
                    messages=[
                        TextMessage(
                            text=f"你已選擇老師：{[t.name for t in teacher_menu if t.id==teacher_id][0]}"
                        )
                    ]
                )
            )
        else:
            # 其他對話流程
            line_bot_api.reply_message_with_http_info(
                ReplyMessageRequest(
                    reply_token=event.reply_token,
                    messages=[
                        TextMessage(text="請輸入「選擇老師」來開始選擇對話老師。")
                    ]
                )
            )








if __name__ == "__main__":
    app.run(port=5000)
