#!/usr/bin/env python3
"""
語音對話模組
基於 AutoGen 多代理分析的語音對話系統實現

功能：
1. 語音轉文字處理
2. 主題式對話管理
3. 即時回饋生成
4. AI 優化範例生成
"""

import os
import json
import logging
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import tempfile

# 設定日誌
logger = logging.getLogger(__name__)

class ConversationTopic(Enum):
    """對話主題類型"""
    INTRODUCTION = "自我介紹"
    TRAVEL = "旅遊"
    SHOPPING = "購物"
    WORKPLACE = "職場"
    TECHNOLOGY = "科技"
    FOOD = "美食"
    HOBBIES = "興趣愛好"
    HEALTH = "健康"
    CULTURE = "文化"
    ENVIRONMENT = "環境"

@dataclass
class VoiceMessage:
    """語音訊息"""
    user_id: str
    message_id: str
    audio_url: str
    duration: int
    timestamp: datetime

@dataclass
class TranscriptionResult:
    """語音轉文字結果"""
    text: str
    confidence: float
    language: str
    duration: float

@dataclass
class FeedbackResult:
    """回饋結果"""
    original_text: str
    grammar_corrections: List[Dict]
    vocabulary_suggestions: List[Dict]
    improved_expressions: List[str]
    translation: str
    optimized_example: str

class VoiceConversationModule:
    """語音對話模組"""
    
    def __init__(self, openai_client, line_bot_api):
        """初始化語音對話模組"""
        self.openai_client = openai_client
        self.line_bot_api = line_bot_api
        self.daily_topics = self._load_daily_topics()
        self.user_conversations = {}  # 用戶對話狀態
    
    def _load_daily_topics(self) -> Dict[str, Dict]:
        """載入每日主題配置"""
        return {
            "beginner": {
                ConversationTopic.INTRODUCTION.value: {
                    "prompt": "今天我們來練習自我介紹！請用英語介紹你自己，包括姓名、年齡、興趣等。",
                    "questions": [
                        "What's your name?",
                        "How old are you?", 
                        "What do you like to do in your free time?",
                        "Where are you from?"
                    ],
                    "vocabulary": ["name", "age", "hobby", "like", "enjoy", "from"]
                },
                ConversationTopic.TRAVEL.value: {
                    "prompt": "讓我們聊聊旅遊！請分享你最喜歡的旅遊地點或夢想中的旅行。",
                    "questions": [
                        "Where would you like to travel?",
                        "What do you like to do when traveling?",
                        "Have you been to any interesting places?"
                    ],
                    "vocabulary": ["travel", "visit", "place", "beautiful", "interesting", "vacation"]
                }
            },
            "intermediate": {
                ConversationTopic.WORKPLACE.value: {
                    "prompt": "今天我們討論職場話題！請分享你的工作經驗或職業規劃。",
                    "questions": [
                        "What kind of work do you do?",
                        "What are your career goals?",
                        "How do you handle workplace challenges?"
                    ],
                    "vocabulary": ["career", "professional", "colleague", "responsibility", "achievement", "challenge"]
                },
                ConversationTopic.TECHNOLOGY.value: {
                    "prompt": "讓我們談談科技！你覺得科技如何改變我們的生活？",
                    "questions": [
                        "How has technology changed your daily life?",
                        "What's your favorite app or gadget?",
                        "Do you think AI will replace human jobs?"
                    ],
                    "vocabulary": ["technology", "digital", "innovation", "artificial intelligence", "smartphone", "internet"]
                }
            },
            "advanced": {
                ConversationTopic.ENVIRONMENT.value: {
                    "prompt": "今天我們深入討論環境議題！請分享你對環保和永續發展的看法。",
                    "questions": [
                        "What environmental issues concern you most?",
                        "How can individuals contribute to environmental protection?",
                        "What role should governments play in addressing climate change?"
                    ],
                    "vocabulary": ["sustainability", "conservation", "renewable energy", "carbon footprint", "biodiversity", "ecosystem"]
                }
            }
        }
    
    def get_daily_topic(self, user_level: str) -> Dict:
        """獲取每日主題"""
        import random
        
        level_topics = self.daily_topics.get(user_level, self.daily_topics["beginner"])
        topic_name = random.choice(list(level_topics.keys()))
        topic_data = level_topics[topic_name]
        
        return {
            "topic": topic_name,
            "prompt": topic_data["prompt"],
            "questions": topic_data["questions"],
            "vocabulary": topic_data["vocabulary"]
        }
    
    def download_voice_message(self, message_id: str, audio_url: str) -> Optional[str]:
        """下載語音訊息檔案"""
        try:
            # 從 LINE 下載語音檔案
            headers = {
                'Authorization': f'Bearer {os.getenv("LINE_CHANNEL_ACCESS_TOKEN")}'
            }
            
            response = requests.get(audio_url, headers=headers)
            response.raise_for_status()
            
            # 保存到臨時檔案
            with tempfile.NamedTemporaryFile(delete=False, suffix='.m4a') as temp_file:
                temp_file.write(response.content)
                return temp_file.name
                
        except Exception as e:
            logger.error(f"下載語音檔案失敗: {e}")
            return None
    
    def transcribe_audio(self, audio_file_path: str) -> TranscriptionResult:
        """語音轉文字"""
        try:
            with open(audio_file_path, 'rb') as audio_file:
                # 使用 OpenAI Whisper API
                transcript = self.openai_client.audio.transcriptions.create(
                    model="whisper-1",
                    file=audio_file,
                    language="en"  # 指定英語
                )
                
                return TranscriptionResult(
                    text=transcript.text,
                    confidence=0.95,  # Whisper 通常有很高的準確度
                    language="en",
                    duration=0.0  # Whisper API 不返回時長
                )
                
        except Exception as e:
            logger.error(f"語音轉文字失敗: {e}")
            return TranscriptionResult(
                text="[語音轉換失敗]",
                confidence=0.0,
                language="unknown",
                duration=0.0
            )
        finally:
            # 清理臨時檔案
            try:
                os.unlink(audio_file_path)
            except:
                pass
    
    def generate_feedback(self, transcribed_text: str, topic: str, user_level: str) -> FeedbackResult:
        """生成即時回饋"""
        try:
            # 簡化的回饋生成，避免 JSON 解析問題
            feedback_prompt = f"""
            請分析以下英語口說內容並提供學習回饋：

            主題：{topic}
            學習者程度：{user_level}
            口說內容："{transcribed_text}"

            請提供：
            1. 中文翻譯
            2. 文法建議（如有錯誤）
            3. 更好的表達方式
            4. 優化範例

            請用清楚的格式回應，適合{user_level}程度的學習者。
            """

            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "你是專業的英語教學助手，請提供簡潔有用的學習回饋。"},
                    {"role": "user", "content": feedback_prompt}
                ],
                max_tokens=500,
                temperature=0.3
            )

            ai_feedback = response.choices[0].message.content.strip()

            # 簡單的文字解析來提取回饋內容
            translation = self._extract_translation(transcribed_text)
            optimized_example = self._generate_optimized_example(transcribed_text, user_level)

            return FeedbackResult(
                original_text=transcribed_text,
                grammar_corrections=[],  # 暫時簡化
                vocabulary_suggestions=[],  # 暫時簡化
                improved_expressions=[],  # 暫時簡化
                translation=translation,
                optimized_example=optimized_example
            )

        except Exception as e:
            logger.error(f"生成回饋失敗: {e}")
            import traceback
            logger.error(f"詳細錯誤: {traceback.format_exc()}")

            # 提供基本的回饋
            return self._generate_basic_feedback(transcribed_text, topic, user_level)

    def _extract_translation(self, text: str) -> str:
        """提取中文翻譯"""
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "請將英文翻譯成中文。"},
                    {"role": "user", "content": f"請翻譯：{text}"}
                ],
                max_tokens=200,
                temperature=0.1
            )
            return response.choices[0].message.content.strip()
        except:
            return "翻譯暫時無法使用"

    def _generate_optimized_example(self, text: str, level: str) -> str:
        """生成優化範例"""
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": f"請將以下英文優化成更自然的表達，適合{level}程度。"},
                    {"role": "user", "content": f"優化這句話：{text}"}
                ],
                max_tokens=200,
                temperature=0.3
            )
            return response.choices[0].message.content.strip()
        except:
            return "優化範例暫時無法生成"

    def _generate_basic_feedback(self, text: str, topic: str, level: str) -> FeedbackResult:
        """生成基本回饋（備用方案）"""
        return FeedbackResult(
            original_text=text,
            grammar_corrections=[],
            vocabulary_suggestions=[],
            improved_expressions=[],
            translation="很好的嘗試！繼續練習會更進步。",
            optimized_example=f"針對{topic}主題，您可以嘗試更詳細地表達您的想法。"
        )
    
    def format_feedback_message(self, feedback: FeedbackResult) -> str:
        """格式化回饋訊息"""
        message = f"""
🎤 **語音轉文字結果：**
"{feedback.original_text}"

📝 **中文翻譯：**
{feedback.translation}

---
        """.strip()
        
        # 文法修正
        if feedback.grammar_corrections:
            message += "\n\n✏️ **文法修正：**\n"
            for correction in feedback.grammar_corrections:
                message += f"• {correction['original']} → {correction['corrected']}\n"
                message += f"  💡 {correction['explanation']}\n"
        
        # 詞彙建議
        if feedback.vocabulary_suggestions:
            message += "\n📚 **詞彙建議：**\n"
            for suggestion in feedback.vocabulary_suggestions:
                message += f"• **{suggestion['word']}**: {suggestion['usage']}\n"
                message += f"  例句: {suggestion['example']}\n"
        
        # 更好的表達
        if feedback.improved_expressions:
            message += "\n🌟 **更自然的表達：**\n"
            for expression in feedback.improved_expressions:
                message += f"• {expression}\n"
        
        # 優化範例
        if feedback.optimized_example:
            message += f"\n✨ **AI 優化範例：**\n\"{feedback.optimized_example}\"\n"
            message += "\n💡 您可以模仿這個範例來提升表達能力！"
        
        return message
    
    def process_voice_message(self, user_id: str, message_id: str, audio_url: str, 
                            topic: str, user_level: str) -> str:
        """處理語音訊息的完整流程"""
        try:
            # 1. 下載語音檔案
            audio_file = self.download_voice_message(message_id, audio_url)
            if not audio_file:
                return "抱歉，無法下載您的語音訊息。請重新錄製。"
            
            # 2. 語音轉文字
            transcription = self.transcribe_audio(audio_file)
            if transcription.confidence < 0.5:
                return "抱歉，語音識別失敗。請確保錄音清晰並重新嘗試。"
            
            # 3. 生成回饋
            feedback = self.generate_feedback(transcription.text, topic, user_level)
            
            # 4. 格式化回饋訊息
            feedback_message = self.format_feedback_message(feedback)
            
            # 5. 記錄對話歷史
            if user_id not in self.user_conversations:
                self.user_conversations[user_id] = []
            
            self.user_conversations[user_id].append({
                "timestamp": datetime.now(),
                "topic": topic,
                "original_audio": audio_url,
                "transcription": transcription.text,
                "feedback": feedback
            })
            
            return feedback_message
            
        except Exception as e:
            logger.error(f"處理語音訊息失敗: {e}")
            return "處理語音訊息時發生錯誤，請稍後再試。"
