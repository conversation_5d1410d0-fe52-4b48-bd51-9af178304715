#!/usr/bin/env python3
"""
AutoGen 風格的 LINE Bot - 修正版本
基於多代理分析的最佳實踐實現

Agent1 分析: OpenAI 客戶端初始化問題
Agent2 驗證: 異步/同步處理混合問題  
Agent3 優化: 提供生產就緒的解決方案
"""

import os
import logging
import asyncio
from datetime import datetime
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

def main():
    """主函數 - AutoGen 風格的結構化實現"""
    
    # 設定日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    print("啟動 Line English Bot (AutoGen 優化版)")
    print("=" * 40)
    
    # 檢查環境變數
    required_env_vars = [
        'LINE_CHANNEL_ACCESS_TOKEN',
        'LINE_CHANNEL_SECRET', 
        'OPENAI_API_KEY'
    ]
    
    for var in required_env_vars:
        value = os.getenv(var)
        if not value:
            print(f"❌ {var}: 未設定")
            return
        else:
            print(f"✅ {var}: {value[:10]}...")
    
    # 導入套件
    try:
        from fastapi import FastAPI, Request, HTTPException
        from fastapi.responses import JSONResponse
        import uvicorn
        from linebot.v3 import WebhookHandler
        from linebot.v3.exceptions import InvalidSignatureError
        from linebot.v3.messaging import Configuration, MessagingApi, ReplyMessageRequest, TextMessage, ApiClient
        from linebot.v3.webhooks import MessageEvent, TextMessageContent, AudioMessageContent
        
        # Agent1 建議: 使用正確的 OpenAI 客戶端
        from openai import OpenAI

        # 導入分級測驗模組
        from assessment_integration import AssessmentIntegration

        # 導入語音對話模組
        from voice_conversation_module import VoiceConversationModule
        from topic_management_module import TopicManagementModule

        print("所有套件導入成功")
        
    except ImportError as e:
        print(f"❌ 套件導入失敗: {e}")
        return
    
    # Agent2 驗證: 正確的配置設定
    line_config = Configuration(access_token=os.getenv('LINE_CHANNEL_ACCESS_TOKEN'))
    handler = WebhookHandler(os.getenv('LINE_CHANNEL_SECRET'))
    
    # Agent3 優化: 使用同步 OpenAI 客戶端避免異步問題
    openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

    # 初始化分級測驗系統
    assessment_integration = AssessmentIntegration(openai_client)

    # 初始化語音對話系統
    voice_conversation = VoiceConversationModule(openai_client, None)  # line_bot_api 稍後設定
    topic_management = TopicManagementModule()

    # 建立 FastAPI 應用
    app = FastAPI(
        title="Line English Learning Bot (AutoGen Optimized)",
        description="英語學習 Line Bot - AutoGen 多代理優化版本",
        version="2.0.0"
    )
    
    # 學員資料存儲
    student_profiles = {}
    
    # 根路由
    @app.get("/")
    def root():
        return {
            "message": "Line English Learning Bot is running! (AutoGen Optimized)",
            "status": "healthy",
            "version": "2.0.0",
            "timestamp": datetime.now().isoformat()
        }
    
    # 健康檢查
    @app.get("/health")
    def health_check():
        return {"status": "healthy", "version": "autogen-optimized"}
    
    # Agent3 優化: 改進的 AI 回應函數 (同步版本)
    def get_ai_response_sync(user_message: str, user_level: str = "beginner") -> str:
        """獲取 AI 回應 - 同步版本避免異步問題"""
        try:
            system_message = f"""
            你是一位友善的英語老師 Emma。學員的英語程度是 {user_level}。
            請用英語回應，並提供學習建議。保持鼓勵和支持的態度。
            如果學員用中文提問，可以適當使用中文解釋。
            回應要簡潔明了，不超過 200 字。
            """
            
            # Agent1 建議: 使用正確的同步 API 調用
            response = openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                max_tokens=300,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"AI 回應錯誤: {e}")
            # Agent2 驗證: 提供詳細錯誤日誌
            import traceback
            logger.error(f"完整錯誤追蹤: {traceback.format_exc()}")
            return "Sorry, I'm having some technical difficulties. Let's try again! 😊"
    
    # Line Callback 端點
    @app.post("/callback")
    async def callback(request: Request):
        """處理 Line Callback"""
        logger.info("=== 收到 Line Callback 請求 ===")
        
        # 獲取簽名和請求體
        signature = request.headers.get('X-Line-Signature', '')
        body = await request.body()
        body_str = body.decode('utf-8')
        
        logger.info(f"X-Line-Signature: {signature[:20]}..." if signature else "X-Line-Signature: 未提供")
        logger.info(f"Request body length: {len(body_str)}")
        
        if not signature:
            logger.error("❌ 缺少 X-Line-Signature 標頭")
            raise HTTPException(status_code=400, detail="Missing X-Line-Signature header")
        
        try:
            handler.handle(body_str, signature)
            logger.info("✅ Webhook 處理成功")
            return JSONResponse(content={"status": "ok"})
            
        except InvalidSignatureError as e:
            logger.error(f"❌ 無效的 Line 簽名: {e}")
            raise HTTPException(status_code=400, detail="Invalid signature")
            
        except Exception as e:
            logger.error(f"❌ Callback 處理錯誤: {e}")
            import traceback
            logger.error(f"完整錯誤: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail="Internal server error")
    
    # Agent3 優化: Line 訊息處理 (同步版本)
    @handler.add(MessageEvent, message=TextMessageContent)
    def handle_text_message(event):
        """處理文字訊息 - AutoGen 優化版本"""
        user_id = event.source.user_id
        user_message = event.message.text.strip()
        
        logger.info(f"收到訊息: {user_message} (來自: {user_id})")
        
        # 初始化學員檔案
        if user_id not in student_profiles:
            student_profiles[user_id] = {
                "user_id": user_id,
                "english_level": "beginner",
                "conversation_count": 0,
                "created_at": datetime.now()
            }
        
        profile = student_profiles[user_id]
        
        # 特殊命令處理
        if user_message.lower() in ['/start', '開始', 'start']:
            # 檢查用戶是否已完成分級測驗
            user_level = assessment_integration.get_user_level(user_id)
            if user_level is None:
                # 新用戶，開始分級測驗
                response = assessment_integration.start_assessment(user_id)
            else:
                # 已完成分級的用戶，顯示歡迎訊息
                response = f"""
🌟 Welcome back to English Learning Bot! (AutoGen Optimized)

Hi! I'm Emma, your AI English teacher.

Your current level: {user_level.value.title()}
Conversations: {profile['conversation_count']}

Let's practice English together! You can:
• Send me any message to start a conversation
• Ask questions about English
• Practice speaking with me
• Send /assessment to retake the level test

What would you like to talk about today? 😊
                """.strip()
        
        elif user_message.lower() in ['/help', '幫助', 'help']:
            response = """
📚 **English Learning Bot 使用指南**

🗣️ **文字對話**: 發送英語訊息進行對話練習
🎤 **語音對話**: 錄製語音訊息獲得即時回饋

💬 **指令列表**:
   • /start - 開始學習旅程（含分級測驗）
   • /help - 顯示此幫助訊息
   • /assessment - 進行或重新分級測驗
   • /topic - 獲取今日對話主題
   • /progress - 查看學習進度
   • /reset - 重置分級結果

🌟 **主要功能**:
   • 🎯 個人化程度分級
   • 📚 每日主題式對話
   • 🎤 語音轉文字 + 即時回饋
   • ✨ AI 優化表達範例
   • 📊 學習進度追蹤

🚀 **開始使用**:
1. 完成分級測驗 (/start)
2. 獲取今日主題 (/topic)
3. 錄製語音或發送文字回應
4. 獲得個人化學習回饋

Ready to practice? Let's go! 😊
            """.strip()

        elif user_message.lower() in ['/assessment', '分級測驗', 'assessment']:
            # 重新開始分級測驗
            response = assessment_integration.start_assessment(user_id)

        elif user_message.lower() in ['/reset', '重置', 'reset']:
            # 重置分級測驗
            response = assessment_integration.reset_assessment(user_id)

        elif user_message.lower() in ['/topic', '主題', 'topic']:
            # 獲取每日主題
            user_level = assessment_integration.get_user_level(user_id)
            if user_level is None:
                response = "請先完成分級測驗！發送 /start 開始。"
            else:
                daily_topic = topic_management.get_daily_topic(user_id, user_level.value)
                response = topic_management.format_topic_message(daily_topic)

        elif user_message.lower() in ['/progress', '進度', 'progress']:
            # 查看學習進度
            progress = topic_management.get_user_progress_summary(user_id)
            response = f"""
📊 **您的學習進度**

🎯 **主題統計：**
• 總共學習主題：{progress['total_topics']}
• 已完成主題：{progress['completed_topics']}
• 進行中主題：{progress['in_progress']}

💪 繼續加油！發送 /topic 獲取今日主題練習。
            """.strip()
        
        else:
            # 檢查是否有進行中的分級測驗
            if assessment_integration.is_assessment_in_progress(user_id):
                # 處理分級測驗回答
                response = assessment_integration.process_assessment_response(user_id, user_message)
            else:
                # 正常的 AI 對話
                try:
                    # 獲取用戶等級進行個人化回應
                    user_level = assessment_integration.get_user_level(user_id)
                    level_str = user_level.value if user_level else profile['english_level']

                    response = get_ai_response_sync(user_message, level_str)
                    profile['conversation_count'] += 1
                    logger.info(f"AI 回應成功生成，長度: {len(response)}")
                except Exception as e:
                    logger.error(f"AI 回應生成失敗: {e}")
                    response = "Sorry, I'm having some technical difficulties. Let's try again! 😊"
        
        # 發送回覆
        try:
            with ApiClient(line_config) as api_client:
                line_bot_api = MessagingApi(api_client)
                line_bot_api.reply_message(
                    ReplyMessageRequest(
                        reply_token=event.reply_token,
                        messages=[TextMessage(text=response)]
                    )
                )
            logger.info("✅ 回覆發送成功")
        except Exception as e:
            logger.error(f"❌ 發送回覆失敗: {e}")

    # 語音訊息處理器
    @handler.add(MessageEvent, message=AudioMessageContent)
    def handle_audio_message(event):
        """處理語音訊息 - 語音對話功能"""
        user_id = event.source.user_id
        message_id = event.message.id
        duration = event.message.duration

        logger.info(f"收到語音訊息: {message_id} (時長: {duration}ms, 來自: {user_id})")

        try:
            # 檢查用戶是否已完成分級測驗
            user_level = assessment_integration.get_user_level(user_id)
            if user_level is None:
                response = """
🎤 **語音功能需要先完成分級測驗**

請先發送 /start 完成英語程度測驗，然後就可以使用語音對話功能了！

完成分級後，您可以：
• 錄製語音回應每日主題
• 獲得即時語音轉文字
• 收到詳細的學習回饋
• 學習AI優化的表達範例
                """.strip()
            else:
                # 獲取語音檔案 URL
                with ApiClient(line_config) as api_client:
                    line_bot_api = MessagingApi(api_client)
                    # 這裡需要獲取語音檔案的實際 URL
                    # LINE Bot API 需要特殊處理來獲取媒體內容
                    audio_url = f"https://api-data.line.me/v2/bot/message/{message_id}/content"

                # 獲取當前主題（簡化版，實際應該追蹤用戶當前主題）
                daily_topic = topic_management.get_daily_topic(user_id, user_level.value)

                # 處理語音訊息
                response = voice_conversation.process_voice_message(
                    user_id, message_id, audio_url, daily_topic.name, user_level.value
                )

                # 更新學習進度
                topic_management.update_user_progress(user_id, daily_topic.id)

        except Exception as e:
            logger.error(f"處理語音訊息失敗: {e}")
            response = """
🎤 **語音處理暫時無法使用**

抱歉，語音功能目前遇到技術問題。您可以：
• 使用文字繼續對話
• 發送 /topic 獲取今日主題
• 稍後再試語音功能

我們正在努力修復中！ 😊
            """.strip()

        # 發送回覆
        try:
            with ApiClient(line_config) as api_client:
                line_bot_api = MessagingApi(api_client)
                line_bot_api.reply_message(
                    ReplyMessageRequest(
                        reply_token=event.reply_token,
                        messages=[TextMessage(text=response)]
                    )
                )
            logger.info("✅ 語音回覆發送成功")
        except Exception as e:
            logger.error(f"❌ 發送語音回覆失敗: {e}")

    # 啟動服務
    port = 5002
    print(f"\n🚀 啟動 Line Bot 服務 (AutoGen 優化版)")
    print(f"服務地址: http://0.0.0.0:{port}")
    print(f"請確保 ngrok 轉發到端口 {port}:")
    print(f"   ngrok http {port}")
    print(f"API 文檔: http://localhost:{port}/docs")
    print("\n然後在 Line Developers Console 中使用 /callback 路徑")
    print("按 Ctrl+C 停止服務")
    
    try:
        uvicorn.run(app, host="0.0.0.0", port=port, log_level="info")
    except KeyboardInterrupt:
        print("\n✅ 服務已停止")

if __name__ == "__main__":
    main()
