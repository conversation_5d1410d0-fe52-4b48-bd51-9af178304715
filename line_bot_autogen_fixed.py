#!/usr/bin/env python3
"""
AutoGen 風格的 LINE Bot - 修正版本
基於多代理分析的最佳實踐實現

Agent1 分析: OpenAI 客戶端初始化問題
Agent2 驗證: 異步/同步處理混合問題  
Agent3 優化: 提供生產就緒的解決方案
"""

import os
import logging
import asyncio
from datetime import datetime
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

def main():
    """主函數 - AutoGen 風格的結構化實現"""
    
    # 設定日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    print("啟動 Line English Bot (AutoGen 優化版)")
    print("=" * 40)
    
    # 檢查環境變數
    required_env_vars = [
        'LINE_CHANNEL_ACCESS_TOKEN',
        'LINE_CHANNEL_SECRET', 
        'OPENAI_API_KEY'
    ]
    
    for var in required_env_vars:
        value = os.getenv(var)
        if not value:
            print(f"❌ {var}: 未設定")
            return
        else:
            print(f"✅ {var}: {value[:10]}...")
    
    # 導入套件
    try:
        from fastapi import FastAPI, Request, HTTPException
        from fastapi.responses import JSONResponse
        import uvicorn
        from linebot.v3 import WebhookHandler
        from linebot.v3.exceptions import InvalidSignatureError
        from linebot.v3.messaging import Configuration, MessagingApi, ReplyMessageRequest, TextMessage, AudioMessage, ApiClient
        from linebot.v3.webhooks import MessageEvent, TextMessageContent, AudioMessageContent
        from fastapi.staticfiles import StaticFiles
        
        # Agent1 建議: 使用正確的 OpenAI 客戶端
        from openai import OpenAI

        # 導入分級測驗模組
        from assessment_integration import AssessmentIntegration

        # 導入語音對話模組
        from voice_conversation_module import VoiceConversationModule
        from topic_management_module import TopicManagementModule
        from voice_feedback_simple import process_voice_simple
        from voice_response_module import process_voice_enhanced
        from static_audio_server import process_voice_with_static_server, init_audio_server

        print("所有套件導入成功")
        
    except ImportError as e:
        print(f"❌ 套件導入失敗: {e}")
        return
    
    # Agent2 驗證: 正確的配置設定
    line_config = Configuration(access_token=os.getenv('LINE_CHANNEL_ACCESS_TOKEN'))
    handler = WebhookHandler(os.getenv('LINE_CHANNEL_SECRET'))
    
    # Agent3 優化: 使用同步 OpenAI 客戶端避免異步問題
    openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

    # 初始化分級測驗系統
    assessment_integration = AssessmentIntegration(openai_client)

    # 初始化語音對話系統
    voice_conversation = VoiceConversationModule(openai_client, None)  # line_bot_api 稍後設定
    topic_management = TopicManagementModule()

    # 初始化靜態音檔服務器 (使用 ngrok HTTPS URL)
    base_url = "https://626a-180-177-109-13.ngrok-free.app"  # ngrok HTTPS URL
    audio_server = init_audio_server(base_url)

    # 建立 FastAPI 應用
    app = FastAPI(
        title="Line English Learning Bot (AutoGen Optimized)",
        description="英語學習 Line Bot - AutoGen 多代理優化版本",
        version="2.0.0"
    )
    
    # 學員資料存儲
    student_profiles = {}
    
    # 根路由
    @app.get("/")
    def root():
        return {
            "message": "Line English Learning Bot is running! (AutoGen Optimized)",
            "status": "healthy",
            "version": "2.0.0",
            "timestamp": datetime.now().isoformat()
        }
    
    # 健康檢查
    @app.get("/health")
    def health_check():
        return {"status": "healthy", "version": "autogen-optimized"}

    # 添加靜態檔案服務
    try:
        static_dir = "static/audio"
        os.makedirs(static_dir, exist_ok=True)
        app.mount("/audio", StaticFiles(directory=static_dir), name="audio")
        logger.info(f"靜態音檔服務已啟動: /audio -> {static_dir}")
    except Exception as e:
        logger.error(f"靜態檔案服務啟動失敗: {e}")
    
    # Agent3 優化: 改進的 AI 回應函數 (同步版本)
    def get_ai_response_sync(user_message: str, user_level: str = "beginner") -> str:
        """獲取 AI 回應 - 同步版本避免異步問題"""
        try:
            system_message = f"""
            你是一位友善的英語老師 Emma。學員的英語程度是 {user_level}。
            請用英語回應，並提供學習建議。保持鼓勵和支持的態度。
            如果學員用中文提問，可以適當使用中文解釋。
            回應要簡潔明了，不超過 200 字。
            """
            
            # Agent1 建議: 使用正確的同步 API 調用
            response = openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                max_tokens=300,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"AI 回應錯誤: {e}")
            # Agent2 驗證: 提供詳細錯誤日誌
            import traceback
            logger.error(f"完整錯誤追蹤: {traceback.format_exc()}")
            return "Sorry, I'm having some technical difficulties. Let's try again! 😊"
    
    # Line Callback 端點
    @app.post("/callback")
    async def callback(request: Request):
        """處理 Line Callback"""
        logger.info("=== 收到 Line Callback 請求 ===")
        
        # 獲取簽名和請求體
        signature = request.headers.get('X-Line-Signature', '')
        body = await request.body()
        body_str = body.decode('utf-8')
        
        logger.info(f"X-Line-Signature: {signature[:20]}..." if signature else "X-Line-Signature: 未提供")
        logger.info(f"Request body length: {len(body_str)}")
        
        if not signature:
            logger.error("❌ 缺少 X-Line-Signature 標頭")
            raise HTTPException(status_code=400, detail="Missing X-Line-Signature header")
        
        try:
            handler.handle(body_str, signature)
            logger.info("✅ Webhook 處理成功")
            return JSONResponse(content={"status": "ok"})
            
        except InvalidSignatureError as e:
            logger.error(f"❌ 無效的 Line 簽名: {e}")
            raise HTTPException(status_code=400, detail="Invalid signature")
            
        except Exception as e:
            logger.error(f"❌ Callback 處理錯誤: {e}")
            import traceback
            logger.error(f"完整錯誤: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail="Internal server error")
    
    # Agent3 優化: Line 訊息處理 (同步版本)
    @handler.add(MessageEvent, message=TextMessageContent)
    def handle_text_message(event):
        """處理文字訊息 - AutoGen 優化版本"""
        user_id = event.source.user_id
        user_message = event.message.text.strip()
        
        logger.info(f"收到訊息: {user_message} (來自: {user_id})")
        
        # 初始化學員檔案
        if user_id not in student_profiles:
            student_profiles[user_id] = {
                "user_id": user_id,
                "english_level": "beginner",
                "conversation_count": 0,
                "created_at": datetime.now()
            }
        
        profile = student_profiles[user_id]
        
        # 特殊命令處理
        if user_message.lower() in ['/start', '開始', 'start']:
            # 暫時跳過分級測驗，直接歡迎用戶
            response = f"""
🌟 Welcome to English Learning Bot! (AutoGen Optimized)

Hi! I'm Emma, your AI English teacher.

🎯 **快速開始：**
• 發送 /topic 獲取今日對話主題
• 錄製語音訊息進行對話練習
• 發送文字訊息進行對話
• 發送 /help 查看所有功能

🎤 **語音功能：**
直接錄製語音訊息，我會提供：
• 語音轉文字
• 中文翻譯
• 學習建議和回饋

Let's start practicing! 😊

💡 提示：發送 /topic 獲取今日練習主題
            """.strip()
        
        elif user_message.lower() in ['/help', '幫助', 'help']:
            response = """
📚 **English Learning Bot 使用指南**

🗣️ **文字對話**: 發送英語訊息進行對話練習
🎤 **語音對話**: 錄製語音訊息獲得即時回饋

💬 **指令列表**:
   • /start - 開始學習旅程
   • /help - 顯示此幫助訊息
   • /topic - 獲取今日對話主題
   • /progress - 查看學習進度

🌟 **主要功能**:
   • 📚 每日主題式對話
   • 🎤 語音轉文字 + 即時回饋
   • 📝 中文翻譯對照
   • ✨ AI 學習建議
   • 📊 學習進度追蹤

🚀 **快速開始**:
1. 發送 /topic 獲取今日主題
2. 錄製語音或發送文字回應
3. 獲得即時學習回饋
4. 繼續練習提升英語能力

Ready to practice? Let's go! 😊

💡 **提示**: 直接錄製語音訊息就能開始練習！
            """.strip()

        elif user_message.lower() in ['/assessment', '分級測驗', 'assessment']:
            # 重新開始分級測驗
            response = assessment_integration.start_assessment(user_id)

        elif user_message.lower() in ['/reset', '重置', 'reset']:
            # 重置分級測驗
            response = assessment_integration.reset_assessment(user_id)

        elif user_message.lower() in ['/topic', '主題', 'topic']:
            # 獲取每日主題（暫時使用中級程度）
            default_level = "intermediate"  # 暫時預設為中級
            daily_topic = topic_management.get_daily_topic(user_id, default_level)
            response = topic_management.format_topic_message(daily_topic)

        elif user_message.lower() in ['/progress', '進度', 'progress']:
            # 查看學習進度
            progress = topic_management.get_user_progress_summary(user_id)
            response = f"""
📊 **您的學習進度**

🎯 **主題統計：**
• 總共學習主題：{progress['total_topics']}
• 已完成主題：{progress['completed_topics']}
• 進行中主題：{progress['in_progress']}

💪 繼續加油！發送 /topic 獲取今日主題練習。
            """.strip()
        
        else:
            # 暫時跳過分級測驗，直接進行 AI 對話
            try:
                # 使用預設中級程度進行對話
                default_level = "intermediate"
                response = get_ai_response_sync(user_message, default_level)
                profile['conversation_count'] += 1
                logger.info(f"AI 回應成功生成，長度: {len(response)}")
            except Exception as e:
                logger.error(f"AI 回應生成失敗: {e}")
                response = "Sorry, I'm having some technical difficulties. Let's try again! 😊"
        
        # 發送回覆
        try:
            with ApiClient(line_config) as api_client:
                line_bot_api = MessagingApi(api_client)
                line_bot_api.reply_message(
                    ReplyMessageRequest(
                        reply_token=event.reply_token,
                        messages=[TextMessage(text=response)]
                    )
                )
            logger.info("✅ 回覆發送成功")
        except Exception as e:
            logger.error(f"❌ 發送回覆失敗: {e}")

    # 語音訊息處理器 - 最簡版本
    @handler.add(MessageEvent, message=AudioMessageContent)
    def handle_audio_message(event):
        """處理語音訊息 - 最簡除錯版本"""
        user_id = event.source.user_id
        message_id = event.message.id
        duration = event.message.duration

        logger.info(f"🎤 收到語音訊息: {message_id} (時長: {duration}ms, 來自: {user_id})")

        try:
            # 步驟 1: 測試主題獲取
            logger.info("🔧 測試主題獲取...")
            default_level = "intermediate"
            daily_topic = topic_management.get_daily_topic(user_id, default_level)
            logger.info(f"✅ 主題獲取成功: {daily_topic.name}")

            # 步驟 2: 模擬語音轉文字
            mock_transcriptions = [
                "What is the topic today?",
                "How are you?",
                "I want to practice English",
                "Can you help me?",
                "Hello teacher"
            ]

            import random
            mock_text = random.choice(mock_transcriptions)
            logger.info(f"🎭 模擬語音轉文字: {mock_text}")

            # 步驟 3: 測試簡單 AI 回應
            logger.info("🔧 測試 AI 回應生成...")

            try:
                # 生成簡單的老師回應
                teacher_prompt = f"""
                你是 Emma，一位友善的英語老師。學生剛剛說："{mock_text}"
                當前主題是：{daily_topic.name}

                請用 2-3 句簡短的英語回應學生，要自然友善。
                """

                teacher_response = openai_client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": "你是專業的英語老師 Emma。"},
                        {"role": "user", "content": teacher_prompt}
                    ],
                    max_tokens=150,
                    temperature=0.7
                )

                teacher_text = teacher_response.choices[0].message.content.strip()
                logger.info(f"✅ AI 回應生成成功: {teacher_text}")

            except Exception as ai_error:
                logger.error(f"❌ AI 回應生成失敗: {ai_error}")
                teacher_text = "Hello! Thank you for practicing with me. That's great!"

            # 步驟 4: 測試 gTTS 語音生成
            logger.info("🔧 測試 gTTS 語音生成...")

            try:
                from gtts import gTTS
                import tempfile
                import uuid

                # 生成語音檔案
                tts = gTTS(text=teacher_text, lang='en', slow=True)

                # 創建臨時檔案
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                temp_file_path = temp_file.name
                temp_file.close()

                # 保存語音檔案
                tts.save(temp_file_path)

                # 移動到靜態目錄
                file_id = str(uuid.uuid4())
                filename = f"{file_id}.mp3"
                static_file_path = os.path.join("static/audio", filename)

                # 確保目錄存在
                os.makedirs("static/audio", exist_ok=True)

                # 複製檔案
                import shutil
                shutil.copy2(temp_file_path, static_file_path)

                # 生成公開 URL
                voice_url = f"{base_url}/audio/{filename}"

                logger.info(f"✅ gTTS 語音生成成功: {voice_url}")

                # 清理臨時檔案
                try:
                    os.unlink(temp_file_path)
                except:
                    pass

                # 組合完整回應
                response_text = f"""
🎤 **語音處理測試 - 第三階段 (完整版)**

✅ **所有功能測試通過！**
📋 訊息ID: {message_id}
⏱️ 時長: {duration}ms

🎯 **主題：** {daily_topic.name} (難度: {default_level})

🎭 **模擬語音轉文字：**
"{mock_text}"

👩‍🏫 **Emma 老師回應：**
{teacher_text}

🔊 **語音檔案：**
✅ gTTS 語音生成成功
🔗 URL: {voice_url}
📁 檔案: {filename}

🔧 **測試狀態：**
✅ 語音訊息接收
✅ 主題獲取功能
✅ AI 回應生成
✅ gTTS 語音生成
✅ 語音檔案保存
🔄 準備發送語音訊息

🎉 **完整語音功能即將啟用！**
                """.strip()

                # 準備回覆訊息 (文字 + 語音)
                messages = [TextMessage(text=response_text)]

                # 使用 HTTPS URL 發送語音檔案
                logger.info(f"🔊 準備發送語音檔案: {voice_url}")

                # 更新回應文字，包含語音檔案資訊
                response_text += f"""

🔊 **語音檔案狀態：**
✅ gTTS 語音生成成功
📁 檔案已保存到: {static_file_path}
🔗 HTTPS URL: {voice_url}
🎤 準備發送 Emma 老師的語音回覆！
                """

                # 準備回覆訊息 (文字 + 語音)
                messages = [TextMessage(text=response_text)]

                # 添加語音訊息
                try:
                    audio_message = AudioMessage(
                        original_content_url=voice_url,
                        duration=5000  # 5秒預設值
                    )
                    messages.append(audio_message)
                    logger.info(f"✅ 語音訊息已準備: {voice_url}")

                    response_messages = messages

                except Exception as audio_error:
                    logger.error(f"❌ 創建語音訊息失敗: {audio_error}")
                    response_messages = [TextMessage(text=response_text)]

            except Exception as tts_error:
                logger.error(f"❌ gTTS 語音生成失敗: {tts_error}")

                # 回退到純文字回應
                response = f"""
🎤 **語音處理測試 - 第三階段 (部分成功)**

✅ **基本功能正常**
📋 訊息ID: {message_id}
⏱️ 時長: {duration}ms

🎯 **主題：** {daily_topic.name}
🎭 **模擬語音：** "{mock_text}"
👩‍🏫 **Emma 回應：** {teacher_text}

❌ **語音生成失敗：** {str(tts_error)}

🔧 **測試狀態：**
✅ 語音接收、主題獲取、AI 回應
❌ gTTS 語音生成需要修復

💡 **下一步：** 修復語音生成問題
                """.strip()

                response_messages = [TextMessage(text=response)]

            # 發送回覆 (支援多個訊息)
            with ApiClient(line_config) as api_client:
                line_bot_api = MessagingApi(api_client)

                # 檢查是否有多個訊息 (文字 + 語音)
                if 'response_messages' in locals() and isinstance(response_messages, list):
                    line_bot_api.reply_message(
                        ReplyMessageRequest(
                            reply_token=event.reply_token,
                            messages=response_messages
                        )
                    )
                    logger.info(f"✅ 多訊息回覆發送成功 ({len(response_messages)} 個訊息)")
                else:
                    # 單一文字訊息
                    line_bot_api.reply_message(
                        ReplyMessageRequest(
                            reply_token=event.reply_token,
                            messages=[TextMessage(text=response if 'response' in locals() else "測試回應")]
                        )
                    )
                    logger.info("✅ 單一訊息回覆發送成功")

        except Exception as e:
            logger.error(f"❌ 最簡語音處理失敗: {e}")
            import traceback
            logger.error(f"詳細錯誤: {traceback.format_exc()}")

            # 備用回覆
            try:
                with ApiClient(line_config) as api_client:
                    line_bot_api = MessagingApi(api_client)
                    line_bot_api.reply_message(
                        ReplyMessageRequest(
                            reply_token=event.reply_token,
                            messages=[TextMessage(text=f"語音處理異常: {str(e)}")]
                        )
                    )
                logger.info("✅ 異常回覆發送成功")
            except Exception as backup_error:
                logger.error(f"❌ 備用回覆也失敗: {backup_error}")

    # 啟動服務
    port = 5002
    print(f"\n🚀 啟動 Line Bot 服務 (AutoGen 優化版)")
    print(f"服務地址: http://0.0.0.0:{port}")
    print(f"請確保 ngrok 轉發到端口 {port}:")
    print(f"   ngrok http {port}")
    print(f"API 文檔: http://localhost:{port}/docs")
    print("\n然後在 Line Developers Console 中使用 /callback 路徑")
    print("按 Ctrl+C 停止服務")
    
    try:
        uvicorn.run(app, host="0.0.0.0", port=port, log_level="info")
    except KeyboardInterrupt:
        print("\n✅ 服務已停止")

if __name__ == "__main__":
    main()
