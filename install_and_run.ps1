# AutoGen Line Bot 安裝和啟動腳本 (PowerShell)

Write-Host "========================================" -ForegroundColor Green
Write-Host "AutoGen Line Bot 安裝和啟動腳本" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 檢查 Python
Write-Host "`n1. 檢查 Python 版本..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python 未安裝或未加入 PATH" -ForegroundColor Red
    Read-Host "按 Enter 鍵退出"
    exit 1
}

# 升級 pip
Write-Host "`n2. 升級 pip..." -ForegroundColor Yellow
python -m pip install --upgrade pip

# 安裝依賴
Write-Host "`n3. 安裝基本依賴..." -ForegroundColor Yellow
$packages = @(
    "fastapi==0.104.1",
    "uvicorn[standard]==0.24.0", 
    "line-bot-sdk==3.7.0",
    "openai",
    "requests",
    "pydantic==2.5.0"
)

foreach ($package in $packages) {
    Write-Host "安裝 $package..." -ForegroundColor Cyan
    pip install $package
}

# 檢查環境變數
Write-Host "`n4. 檢查環境變數..." -ForegroundColor Yellow

$envVars = @("LINE_CHANNEL_ACCESS_TOKEN", "LINE_CHANNEL_SECRET", "OPENAI_API_KEY")
$missingVars = @()

foreach ($var in $envVars) {
    $value = [Environment]::GetEnvironmentVariable($var)
    if ([string]::IsNullOrEmpty($value)) {
        Write-Host "⚠️ $var 未設定" -ForegroundColor Yellow
        $missingVars += $var
    } else {
        Write-Host "✅ $var 已設定" -ForegroundColor Green
    }
}

if ($missingVars.Count -gt 0) {
    Write-Host "`n請設定以下環境變數或編輯 .env 文件:" -ForegroundColor Yellow
    foreach ($var in $missingVars) {
        Write-Host "  $var" -ForegroundColor Red
    }
    
    # 檢查 .env 文件
    if (-not (Test-Path ".env")) {
        if (Test-Path ".env.example") {
            Copy-Item ".env.example" ".env"
            Write-Host "`n已複製 .env.example 為 .env" -ForegroundColor Green
            Write-Host "請編輯 .env 文件設定您的 API 金鑰" -ForegroundColor Yellow
        }
    }
}

# 測試安裝
Write-Host "`n5. 測試安裝..." -ForegroundColor Yellow
python test_installation.py

# 啟動 Bot
Write-Host "`n6. 啟動 Line Bot..." -ForegroundColor Yellow
python run_simple_line_bot.py

Read-Host "`n按 Enter 鍵退出"
