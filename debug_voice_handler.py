#!/usr/bin/env python3
"""
語音處理除錯版本
逐步測試語音功能的各個環節
"""

import os
import logging
from dotenv import load_dotenv
from openai import OpenAI

# 載入環境變數
load_dotenv()

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def debug_voice_processing():
    """除錯語音處理流程"""
    
    print("🔧 語音處理除錯")
    print("=" * 50)
    
    # 1. 測試 OpenAI 連接
    try:
        openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        print("✅ OpenAI 客戶端初始化成功")
    except Exception as e:
        print(f"❌ OpenAI 客戶端初始化失敗: {e}")
        return
    
    # 2. 測試模組導入
    try:
        from voice_conversation_module import VoiceConversationModule
        from topic_management_module import TopicManagementModule
        from static_audio_server import process_voice_with_static_server
        print("✅ 語音模組導入成功")
    except Exception as e:
        print(f"❌ 語音模組導入失敗: {e}")
        return
    
    # 3. 測試模組初始化
    try:
        voice_conversation = VoiceConversationModule(openai_client, None)
        topic_management = TopicManagementModule()
        print("✅ 語音模組初始化成功")
    except Exception as e:
        print(f"❌ 語音模組初始化失敗: {e}")
        return
    
    # 4. 測試主題獲取
    try:
        user_id = "test_user"
        default_level = "intermediate"
        daily_topic = topic_management.get_daily_topic(user_id, default_level)
        print(f"✅ 主題獲取成功: {daily_topic.name}")
    except Exception as e:
        print(f"❌ 主題獲取失敗: {e}")
        return
    
    # 5. 測試語音轉文字（模擬）
    try:
        # 模擬語音轉文字結果
        test_text = "What is the topic today?"
        print(f"✅ 語音轉文字測試: {test_text}")
    except Exception as e:
        print(f"❌ 語音轉文字測試失敗: {e}")
        return
    
    # 6. 測試完整語音處理
    try:
        base_url = "http://localhost:5002"
        feedback_text, audio_url = process_voice_with_static_server(
            openai_client, test_text, daily_topic.name, default_level, base_url
        )
        print(f"✅ 完整語音處理成功")
        print(f"回饋長度: {len(feedback_text)} 字元")
        print(f"語音 URL: {audio_url}")
    except Exception as e:
        print(f"❌ 完整語音處理失敗: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n🎉 語音處理除錯完成！")

def create_simple_voice_handler():
    """創建簡化的語音處理函數"""
    
    print("\n🔧 創建簡化語音處理函數")
    print("=" * 50)
    
    handler_code = '''
def handle_audio_message_simple(event, openai_client, topic_management, base_url):
    """簡化的語音訊息處理"""
    user_id = event.source.user_id
    message_id = event.message.id
    duration = event.message.duration
    
    logger.info(f"🎤 收到語音訊息: {message_id}")
    
    try:
        # 1. 基本回應測試
        response = f"""
🎤 **語音訊息已收到！**

訊息ID: {message_id}
時長: {duration}ms

🔄 **處理狀態：**
✅ 語音訊息接收成功
⏳ 正在處理中...

💡 **提示：**
語音功能正在優化中，請稍後再試！
        """.strip()
        
        return response
        
    except Exception as e:
        logger.error(f"語音處理失敗: {e}")
        return "語音功能暫時無法使用，請使用文字訊息。"
    '''
    
    print("✅ 簡化語音處理函數已準備")
    print("可以複製到 LINE Bot 中使用")
    
    return handler_code

if __name__ == "__main__":
    debug_voice_processing()
    create_simple_voice_handler()
    
    print("\n📋 除錯建議：")
    print("1. 先確保基本語音訊息接收正常")
    print("2. 逐步添加語音轉文字功能")
    print("3. 最後整合 gTTS 語音回覆")
    print("4. 檢查 LINE Bot 日誌輸出")
    print("\n💡 如果語音訊息仍無回應，請檢查：")
    print("• LINE Webhook 設定是否正確")
    print("• ngrok 是否正常運行")
    print("• 服務器日誌是否有錯誤訊息")
