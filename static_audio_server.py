#!/usr/bin/env python3
"""
靜態音檔服務器
為 LINE Bot 提供語音檔案的公開存取

功能：
1. 提供靜態音檔檔案服務
2. 管理音檔檔案生命週期
3. 生成可公開存取的 URL
"""

import os
import shutil
import logging
from datetime import datetime, timedelta
from typing import Optional
import uuid

# 設定日誌
logger = logging.getLogger(__name__)

class StaticAudioServer:
    """靜態音檔服務器"""
    
    def __init__(self, base_url: str = "https://your-domain.com", audio_dir: str = "static/audio"):
        """
        初始化靜態音檔服務器
        
        Args:
            base_url: 服務器的基礎 URL
            audio_dir: 音檔存放目錄
        """
        self.base_url = base_url.rstrip('/')
        self.audio_dir = audio_dir
        self.ensure_audio_directory()
    
    def ensure_audio_directory(self):
        """確保音檔目錄存在"""
        try:
            os.makedirs(self.audio_dir, exist_ok=True)
            logger.info(f"音檔目錄已準備: {self.audio_dir}")
        except Exception as e:
            logger.error(f"創建音檔目錄失敗: {e}")
    
    def save_audio_file(self, temp_file_path: str, duration_hours: int = 24) -> Optional[str]:
        """
        保存音檔檔案到靜態目錄並返回公開 URL
        
        Args:
            temp_file_path: 臨時檔案路徑
            duration_hours: 檔案保存時長（小時）
            
        Returns:
            可公開存取的 URL
        """
        try:
            # 生成唯一檔案名
            file_id = str(uuid.uuid4())
            filename = f"{file_id}.mp3"
            static_file_path = os.path.join(self.audio_dir, filename)
            
            # 複製檔案到靜態目錄
            shutil.copy2(temp_file_path, static_file_path)
            
            # 生成公開 URL
            public_url = f"{self.base_url}/audio/{filename}"
            
            # 記錄檔案資訊（用於後續清理）
            self._record_file_info(filename, duration_hours)
            
            logger.info(f"音檔檔案已保存: {static_file_path} -> {public_url}")
            return public_url
            
        except Exception as e:
            logger.error(f"保存音檔檔案失敗: {e}")
            return None
    
    def _record_file_info(self, filename: str, duration_hours: int):
        """記錄檔案資訊用於清理"""
        try:
            info_file = os.path.join(self.audio_dir, "file_info.txt")
            expiry_time = datetime.now() + timedelta(hours=duration_hours)
            
            with open(info_file, "a", encoding="utf-8") as f:
                f.write(f"{filename},{expiry_time.isoformat()}\n")
                
        except Exception as e:
            logger.error(f"記錄檔案資訊失敗: {e}")
    
    def cleanup_expired_files(self):
        """清理過期的音檔檔案"""
        try:
            info_file = os.path.join(self.audio_dir, "file_info.txt")
            if not os.path.exists(info_file):
                return
            
            current_time = datetime.now()
            valid_files = []
            
            with open(info_file, "r", encoding="utf-8") as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        filename, expiry_str = line.split(",", 1)
                        expiry_time = datetime.fromisoformat(expiry_str)
                        
                        if current_time < expiry_time:
                            # 檔案未過期
                            valid_files.append(line)
                        else:
                            # 檔案已過期，刪除
                            file_path = os.path.join(self.audio_dir, filename)
                            if os.path.exists(file_path):
                                os.unlink(file_path)
                                logger.info(f"已刪除過期音檔: {filename}")
                    except Exception as e:
                        logger.error(f"處理檔案記錄失敗: {line}, 錯誤: {e}")
            
            # 更新檔案資訊
            with open(info_file, "w", encoding="utf-8") as f:
                for line in valid_files:
                    f.write(line + "\n")
                    
        except Exception as e:
            logger.error(f"清理過期檔案失敗: {e}")

# 全域音檔服務器實例
audio_server = None

def init_audio_server(base_url: str = "http://localhost:5002"):
    """初始化音檔服務器"""
    global audio_server
    audio_server = StaticAudioServer(base_url)
    return audio_server

def get_audio_server():
    """獲取音檔服務器實例"""
    global audio_server
    if audio_server is None:
        audio_server = init_audio_server()
    return audio_server

class GTTSWithStaticServer:
    """整合靜態服務器的 gTTS 模組"""
    
    def __init__(self, openai_client, base_url: str = "http://localhost:5002"):
        """初始化整合模組"""
        self.openai_client = openai_client
        self.audio_server = StaticAudioServer(base_url)
        
        # 導入 gTTS 模組
        from gtts_voice_module import EnhancedVoiceFeedbackWithGTTS
        self.voice_feedback = EnhancedVoiceFeedbackWithGTTS(openai_client)
    
    def generate_voice_feedback_with_url(self, transcribed_text: str, topic: str, user_level: str) -> tuple:
        """
        生成語音回饋並返回可公開存取的 URL
        
        Returns:
            (feedback_text, public_audio_url)
        """
        try:
            # 生成語音回饋
            feedback_text, temp_voice_file = self.voice_feedback.generate_voice_feedback(
                transcribed_text, topic, user_level
            )
            
            public_url = None
            if temp_voice_file and os.path.exists(temp_voice_file):
                # 保存到靜態目錄並獲取公開 URL
                public_url = self.audio_server.save_audio_file(temp_voice_file)
                
                # 清理臨時檔案
                try:
                    os.unlink(temp_voice_file)
                except:
                    pass
            
            # 更新回饋訊息包含 URL 資訊
            if public_url:
                feedback_text = feedback_text.replace(
                    "✅ 語音檔案已生成！",
                    f"✅ 語音檔案已生成！\n🔗 語音 URL: {public_url}"
                )
            
            return feedback_text, public_url
            
        except Exception as e:
            logger.error(f"生成語音回饋失敗: {e}")
            fallback_text = f"""
🎤 **您說的話：**
"{transcribed_text}"

👩‍🏫 **Emma 老師回應：**
Thank you for practicing with me! That was great!

🔊 **語音狀態：**
語音功能暫時無法使用，但您的練習很棒！

💪 繼續加油！ 😊
            """.strip()
            return fallback_text, None
    
    def cleanup(self):
        """清理資源"""
        self.voice_feedback.cleanup()
        self.audio_server.cleanup_expired_files()

# 整合函數
def process_voice_with_static_server(openai_client, transcribed_text: str, topic: str, user_level: str, base_url: str = "http://localhost:5002") -> tuple:
    """
    使用靜態服務器的語音處理函數
    
    Returns:
        (feedback_text, public_audio_url)
    """
    try:
        processor = GTTSWithStaticServer(openai_client, base_url)
        result = processor.generate_voice_feedback_with_url(transcribed_text, topic, user_level)
        processor.cleanup()
        return result
    except Exception as e:
        logger.error(f"靜態服務器語音處理失敗: {e}")
        fallback_text = f"""
🎤 **您說的話：**
"{transcribed_text}"

👩‍🏫 **Emma 老師回應：**
Thank you for practicing with me! That was great!

🔊 **語音狀態：**
語音功能暫時無法使用，但您的練習很棒！

💪 繼續加油！ 😊
        """.strip()
        return fallback_text, None
